#!/usr/bin/env python3
"""
测试更新模块的脚本

用于验证修复后的更新模块是否正常工作
"""

import sys
import time
from update import UpdateManager, check_update_once

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试更新模块基本功能")
    print("=" * 60)
    
    # 测试1: 创建更新管理器
    print("\n1. 测试创建更新管理器...")
    try:
        updater = UpdateManager("test_app", "1.0.0")
        print("✓ 更新管理器创建成功")
    except Exception as e:
        print(f"✗ 更新管理器创建失败: {e}")
        return False
    
    # 测试2: 手动检查更新
    print("\n2. 测试手动检查更新...")
    try:
        has_update = updater.check_once()
        if has_update:
            print("✓ 发现新版本，GUI应该已显示")
        else:
            print("✓ 当前已是最新版本")
    except Exception as e:
        print(f"✗ 检查更新失败: {e}")
        return False
    
    # 测试3: 便利函数
    print("\n3. 测试便利函数...")
    try:
        has_update = check_update_once("test_app", "1.0.0")
        print(f"✓ 便利函数工作正常，返回: {has_update}")
    except Exception as e:
        print(f"✗ 便利函数失败: {e}")
        return False
    
    # 测试4: 停止管理器
    print("\n4. 测试停止管理器...")
    try:
        updater.stop()
        print("✓ 更新管理器停止成功")
    except Exception as e:
        print(f"✗ 停止管理器失败: {e}")
        return False
    
    return True

def test_gui_progress():
    """测试GUI进度条功能"""
    print("\n" + "=" * 60)
    print("测试GUI进度条功能")
    print("=" * 60)
    
    try:
        # 创建一个会触发下载的更新管理器
        updater = UpdateManager("imei_tool", "1.0.0")
        print("✓ 更新管理器创建成功")
        
        # 手动检查更新
        print("正在检查更新...")
        has_update = updater.check_once()
        
        if has_update:
            print("✓ 发现更新，GUI窗口应该已显示")
            print("请观察进度条是否正常更新")
        else:
            print("✓ 当前已是最新版本，无需下载")
        
        # 等待一段时间让用户观察
        print("等待5秒后自动停止...")
        time.sleep(5)
        
        updater.stop()
        print("✓ 测试完成")
        
    except Exception as e:
        print(f"✗ GUI测试失败: {e}")
        return False
    
    return True

def test_background_check():
    """测试后台检查功能"""
    print("\n" + "=" * 60)
    print("测试后台检查功能")
    print("=" * 60)
    
    try:
        updater = UpdateManager("test_app", "1.0.0", check_interval=10)  # 10秒检查一次
        print("✓ 更新管理器创建成功")
        
        print("启动后台检查...")
        updater.start_background_check()
        print("✓ 后台检查已启动")
        
        print("等待15秒观察后台检查...")
        time.sleep(15)
        
        print("停止后台检查...")
        updater.stop()
        print("✓ 后台检查已停止")
        
    except Exception as e:
        print(f"✗ 后台检查测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("专业级跨平台更新模块 - 测试脚本")
    print("修复版本：解决进度条不更新、tkinter冲突等问题")
    
    # 基本功能测试
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败")
        return 1
    
    # 询问是否进行GUI测试
    try:
        choice = input("\n是否进行GUI进度条测试？(y/n): ").strip().lower()
        if choice == 'y':
            if not test_gui_progress():
                print("\n❌ GUI测试失败")
                return 1
    except (EOFError, KeyboardInterrupt):
        print("\n跳过GUI测试")
    
    # 询问是否进行后台测试
    try:
        choice = input("\n是否进行后台检查测试？(y/n): ").strip().lower()
        if choice == 'y':
            if not test_background_check():
                print("\n❌ 后台检查测试失败")
                return 1
    except (EOFError, KeyboardInterrupt):
        print("\n跳过后台测试")
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！更新模块工作正常")
    print("=" * 60)
    
    print("\n使用说明：")
    print("1. 手动检查更新：")
    print("   from update import UpdateManager")
    print("   updater = UpdateManager('your_app', '1.0.0')")
    print("   updater.check_once()")
    print()
    print("2. 后台自动检查：")
    print("   updater.start_background_check()")
    print()
    print("3. 便利函数：")
    print("   from update import check_update_once")
    print("   check_update_once('your_app', '1.0.0')")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
