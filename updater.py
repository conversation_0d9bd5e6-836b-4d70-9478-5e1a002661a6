"""
简洁的GUI更新模块

特性：
- GUI进度条显示
- 完善的文件替换逻辑
- 跨平台支持
- 简洁的API

使用方法：
    from updater import check_update
    check_update("软件名", "1.0.0")
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import shutil
import zipfile
import platform
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox


class UpdaterGUI:
    """更新GUI界面"""
    
    def __init__(self, software_name, version):
        self.software_name = software_name
        self.version = version
        self.cancelled = False
        self.window = None
        
    def show_update_dialog(self, update_info):
        """显示更新对话框"""
        message = (f"发现新版本 {update_info['version']}\n"
                  f"当前版本: {self.version}\n\n"
                  f"更新说明:\n{update_info['release_notes']}\n\n"
                  f"是否立即下载更新？")
        
        return messagebox.askyesno("发现新版本", message)
    
    def show_progress_window(self):
        """显示进度窗口"""
        self.window = tk.Tk()
        self.window.title(f"{self.software_name} - 正在下载")
        self.window.geometry("450x120")
        self.window.resizable(False, False)
        
        # 居中显示
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 450) // 2
        y = (screen_height - 120) // 2
        self.window.geometry(f"450x120+{x}+{y}")
        
        # 置顶显示
        self.window.attributes('-topmost', True)
        if platform.system() == 'Windows':
            self.window.attributes('-toolwindow', True)
        
        # 创建界面
        frame = ttk.Frame(self.window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        self.title_label = ttk.Label(frame, text=f"正在下载 {self.version}", 
                                    font=('Arial', 10, 'bold'))
        self.title_label.pack(pady=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(frame, variable=self.progress_var, 
                                          length=400, mode='determinate', maximum=100)
        self.progress_bar.pack(pady=(0, 10))
        
        # 状态标签
        self.status_label = ttk.Label(frame, text="准备下载...")
        self.status_label.pack(pady=(0, 10))
        
        # 取消按钮
        ttk.Button(frame, text="取消", command=self.cancel).pack()
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.cancel)
        
        return self.window
    
    def update_progress(self, percentage, status):
        """更新进度"""
        if self.window and not self.cancelled:
            try:
                self.progress_var.set(percentage)
                self.status_label.config(text=status)
                self.window.update()
            except:
                pass
    
    def cancel(self):
        """取消下载"""
        self.cancelled = True
        if self.window:
            self.window.destroy()
            self.window = None
    
    def close(self):
        """关闭窗口"""
        if self.window:
            self.window.destroy()
            self.window = None


class FileReplacer:
    """文件替换器"""
    
    @staticmethod
    def get_app_dir():
        """获取应用目录"""
        if getattr(sys, 'frozen', False):
            return Path(sys.executable).parent
        else:
            return Path(sys.argv[0]).parent
    
    @staticmethod
    def is_exe_running():
        """检查是否是exe运行"""
        return getattr(sys, 'frozen', False)
    
    @staticmethod
    def get_current_exe():
        """获取当前exe路径"""
        if FileReplacer.is_exe_running():
            return Path(sys.executable)
        else:
            return Path(sys.argv[0])
    
    @staticmethod
    def install_update(file_path, software_name):
        """安装更新"""
        app_dir = FileReplacer.get_app_dir()
        
        if file_path.suffix.lower() == '.zip':
            # ZIP文件解压安装
            FileReplacer._install_zip(file_path, app_dir)
            messagebox.showinfo("更新完成", f"更新安装成功！\n版本已更新。")
            
        elif file_path.suffix.lower() == '.exe':
            current_exe = FileReplacer.get_current_exe()
            
            if FileReplacer.is_exe_running() and file_path.name.lower() == current_exe.name.lower():
                # 同名exe文件，需要替换
                if FileReplacer._replace_exe(current_exe, file_path, software_name):
                    return  # 程序会重启，不显示消息
            
            messagebox.showinfo("下载完成", f"文件已下载到：\n{file_path}\n\n请手动运行新版本。")
        else:
            messagebox.showinfo("下载完成", f"文件已下载到：\n{file_path}")
    
    @staticmethod
    def _install_zip(zip_path, target_dir):
        """安装ZIP文件"""
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(target_dir)
        
        # 设置可执行权限
        if platform.system() != 'Windows':
            for file_path in target_dir.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in ['.exe', '.sh', '.py']:
                    file_path.chmod(0o755)
        
        # 删除ZIP文件
        zip_path.unlink()
    
    @staticmethod
    def _replace_exe(current_exe, new_exe, software_name):
        """替换exe文件"""
        if messagebox.askyesno("更新完成", 
                              f"新版本下载完成！\n是否立即重启以完成更新？"):
            
            # 创建替换脚本
            script_path = FileReplacer._create_replace_script(current_exe, new_exe, software_name)
            
            # 运行脚本并退出
            if platform.system() == 'Windows':
                subprocess.Popen([script_path], shell=True)
            else:
                subprocess.Popen(['bash', script_path])
            
            sys.exit(0)
            return True
        
        return False
    
    @staticmethod
    def _create_replace_script(current_exe, new_exe, software_name):
        """创建替换脚本"""
        temp_dir = Path(tempfile.gettempdir())
        
        if platform.system() == 'Windows':
            script_path = temp_dir / "update_replace.bat"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(f"""@echo off
title {software_name} - 正在更新
echo 正在更新，请稍候...
timeout /t 2 /nobreak > nul
copy /Y "{new_exe}" "{current_exe}"
del "{new_exe}"
start "" "{current_exe}"
del "%~f0"
""")
        else:
            script_path = temp_dir / "update_replace.sh"
            with open(script_path, 'w') as f:
                f.write(f"""#!/bin/bash
echo "{software_name} - 正在更新..."
sleep 2
cp "{new_exe}" "{current_exe}"
chmod +x "{current_exe}"
rm "{new_exe}"
nohup "{current_exe}" > /dev/null 2>&1 &
rm "$0"
""")
            script_path.chmod(0o755)
        
        return script_path


def download_file(url, gui, base_url="https://unlockoko.com"):
    """下载文件"""
    download_url = f"{base_url}{url}"
    
    try:
        response = requests.get(download_url, stream=True, timeout=30)
        response.raise_for_status()
        
        # 获取文件名
        filename = os.path.basename(url.split('?')[0])
        if not filename:
            filename = "update_file"
        
        # 保存路径
        save_path = FileReplacer.get_app_dir() / filename
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if gui.cancelled:
                    f.close()
                    save_path.unlink(missing_ok=True)
                    return None
                
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    if total_size > 0:
                        percentage = (downloaded_size / total_size) * 100
                        status = f"{downloaded_size/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB"
                        gui.update_progress(percentage, status)
        
        return save_path
        
    except Exception as e:
        messagebox.showerror("下载失败", f"下载时发生错误：\n{str(e)}")
        return None


def check_update_api(software_name, current_version, base_url="https://unlockoko.com"):
    """检查更新API"""
    try:
        url = f"{base_url}/api/check_update"
        params = {
            "software_name": software_name.lower(),
            "current_version": current_version
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()
        
        if data.get("code") == "100":
            update_data = data.get("data", {})
            if update_data.get("need_update", False):
                return {
                    "version": update_data.get("version", ""),
                    "download_url": update_data.get("download_url", ""),
                    "release_notes": update_data.get("release_notes", "")
                }
        
        return None
        
    except Exception as e:
        messagebox.showerror("检查更新失败", f"检查更新时发生错误：\n{str(e)}")
        return None


def check_update(software_name, current_version, base_url="https://unlockoko.com"):
    """检查并处理更新 - 主函数"""
    # 检查更新
    update_info = check_update_api(software_name, current_version, base_url)
    
    if not update_info:
        messagebox.showinfo("更新检查", "当前已是最新版本")
        return False
    
    # 创建GUI
    gui = UpdaterGUI(software_name, update_info['version'])
    
    # 询问是否下载
    if not gui.show_update_dialog(update_info):
        return False
    
    # 显示进度窗口
    progress_window = gui.show_progress_window()
    
    # 在新线程中下载
    def download_thread():
        file_path = download_file(update_info['download_url'], gui, base_url)
        
        # 关闭进度窗口
        gui.close()
        
        if file_path and not gui.cancelled:
            # 安装更新
            FileReplacer.install_update(file_path, software_name)
    
    thread = threading.Thread(target=download_thread, daemon=True)
    thread.start()
    
    # 运行GUI
    progress_window.mainloop()
    
    return True


if __name__ == "__main__":
    # 测试
    check_update("imei_tool", "1.0.0")
