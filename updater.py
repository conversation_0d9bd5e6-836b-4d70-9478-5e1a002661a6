"""
简洁的GUI更新模块

特性：
- GUI进度条显示
- 完善的文件替换逻辑
- 跨平台支持
- 简洁的API

使用方法：
    from updater import check_update
    check_update("软件名", "1.0.0")
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import shutil
import zipfile
import platform
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox


class UpdaterGUI:
    """更新GUI界面 - 避免tkinter冲突"""

    def __init__(self, software_name, current_version):
        self.software_name = software_name
        self.current_version = current_version
        self.cancelled = False
        self.window = None
        self.new_version = None  # 新版本号，在show_update_dialog中设置

    def show_update_dialog(self, update_info):
        """显示更新对话框 - 使用独立的tkinter实例"""
        self.new_version = update_info['version']  # 保存新版本号

        # 创建独立的根窗口避免冲突
        dialog_root = tk.Tk()
        dialog_root.withdraw()  # 隐藏主窗口
        dialog_root.attributes('-topmost', True)

        try:
            message = (f"发现新版本 {update_info['version']}\n"
                      f"当前版本: {self.current_version}\n\n"
                      f"更新说明:\n{update_info['release_notes']}\n\n"
                      f"是否立即下载更新？")

            result = messagebox.askyesno("发现新版本", message, parent=dialog_root)
            return result
        finally:
            dialog_root.destroy()

    def show_progress_window(self):
        """显示进度窗口 - 优化界面和布局"""
        # 创建独立的tkinter实例避免冲突
        self.window = tk.Tk()
        self.window.title(f"{self.software_name} - 正在下载")
        self.window.geometry("500x180")  # 增大窗口尺寸
        self.window.resizable(False, False)

        # 居中显示
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 500) // 2
        y = (screen_height - 180) // 2
        self.window.geometry(f"500x180+{x}+{y}")

        # 置顶显示
        self.window.attributes('-topmost', True)
        if platform.system() == 'Windows':
            self.window.attributes('-toolwindow', True)

        # 设置窗口图标和样式
        try:
            # 设置窗口样式
            self.window.configure(bg='#f0f0f0')
        except:
            pass

        # 创建主框架
        main_frame = ttk.Frame(self.window, padding="25")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题 - 增大字体，显示正确的新版本号
        title_text = f"正在下载 {self.new_version}" if self.new_version else "正在下载更新"
        self.title_label = ttk.Label(main_frame,
                                    text=title_text,
                                    font=('Microsoft YaHei', 12, 'bold'))
        self.title_label.pack(pady=(0, 15))

        # 进度条 - 修复参数错误
        self.progress_var = tk.DoubleVar()

        # 创建进度条容器来控制高度
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(pady=(0, 15), fill=tk.X)

        self.progress_bar = ttk.Progressbar(progress_frame,
                                          variable=self.progress_var,
                                          length=450,
                                          mode='determinate',
                                          maximum=100)
        self.progress_bar.pack(pady=5)  # 通过padding控制视觉高度

        # 状态标签 - 优化字体和布局
        self.status_label = ttk.Label(main_frame,
                                     text="准备下载...",
                                     font=('Microsoft YaHei', 9),
                                     foreground='#666666')
        self.status_label.pack(pady=(0, 15))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack()

        # 取消按钮 - 美化样式
        cancel_btn = ttk.Button(button_frame,
                               text="取消下载",
                               command=self.cancel,
                               width=12)
        cancel_btn.pack()

        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.cancel)

        return self.window

    def update_progress(self, percentage, status):
        """更新进度 - 简化版本"""
        if self.window and not self.cancelled:
            try:
                self.progress_var.set(percentage)
                self.status_label.config(text=status)
                self.window.update_idletasks()
            except:
                pass

    def cancel(self):
        """取消下载 - 简化版本"""
        self.cancelled = True
        if self.window:
            self.window.quit()  # 退出mainloop

    def close(self):
        """关闭窗口 - 简化版本"""
        if self.window:
            self.window.quit()  # 退出mainloop


class FileReplacer:
    """文件替换器 - 修复路径处理问题"""

    @staticmethod
    def get_app_dir():
        """获取应用目录 - 处理打包后的路径问题"""
        try:
            if getattr(sys, 'frozen', False):
                # 打包后的exe程序
                if hasattr(sys, '_MEIPASS'):
                    # PyInstaller打包
                    return Path(sys.executable).parent.resolve()
                else:
                    # 其他打包工具
                    return Path(sys.executable).parent.resolve()
            else:
                # 开发环境
                if sys.argv[0]:
                    return Path(sys.argv[0]).parent.resolve()
                else:
                    return Path.cwd()
        except Exception:
            # 备用方案
            return Path.cwd()

    @staticmethod
    def is_exe_running():
        """检查是否是exe运行"""
        return getattr(sys, 'frozen', False)

    @staticmethod
    def get_current_exe():
        """获取当前exe路径 - 修复路径问题"""
        try:
            if FileReplacer.is_exe_running():
                return Path(sys.executable).resolve()
            else:
                if sys.argv[0]:
                    return Path(sys.argv[0]).resolve()
                else:
                    return Path.cwd() / "main.py"
        except Exception:
            return Path.cwd() / "main.py"

    @staticmethod
    def install_update(file_path, software_name):
        """安装更新"""
        app_dir = FileReplacer.get_app_dir()

        if file_path.suffix.lower() == '.zip':
            # ZIP文件解压安装
            FileReplacer._install_zip(file_path, app_dir)
            messagebox.showinfo("更新完成", f"更新安装成功！\n版本已更新。")

        elif file_path.suffix.lower() == '.exe':
            current_exe = FileReplacer.get_current_exe()

            if FileReplacer.is_exe_running() and file_path.name.lower() == current_exe.name.lower():
                # 同名exe文件，需要替换
                if FileReplacer._replace_exe(current_exe, file_path, software_name):
                    return  # 程序会重启，不显示消息

            messagebox.showinfo("下载完成", f"文件已下载到：\n{file_path}\n\n请手动运行新版本。")
        else:
            messagebox.showinfo("下载完成", f"文件已下载到：\n{file_path}")

    @staticmethod
    def _install_zip(zip_path, target_dir):
        """安装ZIP文件"""
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(target_dir)

        # 设置可执行权限
        if platform.system() != 'Windows':
            for file_path in target_dir.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in ['.exe', '.sh', '.py']:
                    file_path.chmod(0o755)

        # 删除ZIP文件
        zip_path.unlink()

    @staticmethod
    def _replace_exe(current_exe, new_exe, software_name):
        """替换exe文件"""
        if messagebox.askyesno("更新完成",
                              f"新版本下载完成！\n是否立即重启以完成更新？"):

            # 创建替换脚本
            script_path = FileReplacer._create_replace_script(current_exe, new_exe, software_name)

            # 运行脚本并退出
            if platform.system() == 'Windows':
                subprocess.Popen([script_path], shell=True)
            else:
                subprocess.Popen(['bash', script_path])

            sys.exit(0)
            return True

        return False

    @staticmethod
    def _create_replace_script(current_exe, new_exe, software_name):
        """创建替换脚本"""
        temp_dir = Path(tempfile.gettempdir())

        if platform.system() == 'Windows':
            script_path = temp_dir / "update_replace.bat"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(f"""@echo off
title {software_name} - 正在更新
echo 正在更新，请稍候...
timeout /t 2 /nobreak > nul
copy /Y "{new_exe}" "{current_exe}"
del "{new_exe}"
start "" "{current_exe}"
del "%~f0"
""")
        else:
            script_path = temp_dir / "update_replace.sh"
            with open(script_path, 'w') as f:
                f.write(f"""#!/bin/bash
echo "{software_name} - 正在更新..."
sleep 2
cp "{new_exe}" "{current_exe}"
chmod +x "{current_exe}"
rm "{new_exe}"
nohup "{current_exe}" > /dev/null 2>&1 &
rm "$0"
""")
            script_path.chmod(0o755)

        return script_path


def download_file(url, gui, base_url="https://unlockoko.com"):
    """下载文件 - 修复路径和文件名处理"""
    download_url = f"{base_url}{url}"

    try:
        response = requests.get(download_url, stream=True, timeout=30)
        response.raise_for_status()

        # 获取文件名 - 改进文件名提取逻辑
        filename = None

        # 1. 尝试从Content-Disposition头获取
        content_disposition = response.headers.get('content-disposition', '')
        if 'filename=' in content_disposition:
            try:
                filename = content_disposition.split('filename=')[1].strip('"\'')
            except:
                pass

        # 2. 从URL获取
        if not filename:
            filename = os.path.basename(url.split('?')[0])

        # 3. 备用文件名
        if not filename or len(filename) < 3:
            filename = f"update_{int(time.time())}.zip"

        # 确保文件名安全
        filename = "".join(c for c in filename if c.isalnum() or c in '.-_').strip()
        if not filename:
            filename = f"update_{int(time.time())}.zip"

        # 保存路径 - 确保目录存在
        app_dir = FileReplacer.get_app_dir()
        app_dir.mkdir(parents=True, exist_ok=True)  # 确保目录存在
        save_path = app_dir / filename

        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0

        # 下载文件
        try:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    # 检查是否取消
                    if gui.cancelled:
                        # 不需要手动close，with语句会处理
                        save_path.unlink(missing_ok=True)
                        return None

                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # 更新进度
                        if total_size > 0:
                            percentage = (downloaded_size / total_size) * 100
                            status = f"{downloaded_size/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB"
                            gui.update_progress(percentage, status)
                        else:
                            # 未知大小时显示已下载量
                            status = f"已下载 {downloaded_size/1024/1024:.1f}MB"
                            gui.update_progress(0, status)
        except Exception as e:
            # 下载过程中的异常，清理文件
            save_path.unlink(missing_ok=True)
            raise e

        # 验证下载完整性
        if total_size > 0 and downloaded_size != total_size:
            save_path.unlink(missing_ok=True)
            raise Exception(f"下载不完整：期望{total_size}字节，实际{downloaded_size}字节")

        # 验证文件是否存在且不为空
        if not save_path.exists() or save_path.stat().st_size == 0:
            save_path.unlink(missing_ok=True)
            raise Exception("下载的文件为空或不存在")

        return save_path

    except requests.exceptions.Timeout:
        raise Exception("下载超时，请检查网络连接")
    except requests.exceptions.ConnectionError:
        raise Exception("网络连接中断")
    except Exception as e:
        # 清理可能的不完整文件
        try:
            if 'save_path' in locals():
                save_path.unlink(missing_ok=True)
        except:
            pass
        raise e


def compare_versions(current, remote):
    """简单的版本比较 - 不过度复杂化"""
    try:
        # 处理空值
        if not remote or str(remote).strip() == '':
            return False
        if not current or str(current).strip() == '':
            return True

        def version_tuple(v):
            import re
            # 提取数字和点
            clean_v = re.sub(r'[^\d.]', '', str(v))
            if not clean_v:
                return (0, 0, 0)

            parts = clean_v.split('.')
            parts = [p for p in parts if p]  # 过滤空字符串

            # 转换为整数
            int_parts = []
            for p in parts:
                try:
                    int_parts.append(int(p))
                except ValueError:
                    int_parts.append(0)

            # 补齐到3位
            while len(int_parts) < 3:
                int_parts.append(0)

            return tuple(int_parts[:3])  # 只取前3位

        current_tuple = version_tuple(current)
        remote_tuple = version_tuple(remote)

        return remote_tuple > current_tuple

    except Exception:
        # 备用：字符串比较
        return str(remote).strip() != str(current).strip()


def check_update_api(software_name, current_version, base_url="https://unlockoko.com"):
    """检查更新API - 修复逻辑问题"""
    try:
        url = f"{base_url}/api/check_update"
        params = {
            "software_name": software_name.lower(),
            "current_version": current_version
        }

        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("code") == "100":
            update_data = data.get("data", {})
            remote_version = update_data.get("version", "")

            # 修复：真正比较版本号，而不是只依赖服务器的need_update字段
            if remote_version and compare_versions(current_version, remote_version):
                return {
                    "version": remote_version,
                    "download_url": update_data.get("download_url", ""),
                    "release_notes": update_data.get("release_notes", "")
                }
            else:
                # 版本相同或更旧
                return None
        else:
            # 服务器返回错误
            error_msg = data.get("msg", "服务器返回未知错误")
            raise Exception(f"服务器错误: {error_msg}")

    except requests.exceptions.Timeout:
        raise Exception("连接超时，请检查网络连接")
    except requests.exceptions.ConnectionError:
        raise Exception("无法连接到服务器，请检查网络连接")
    except requests.exceptions.HTTPError as e:
        raise Exception(f"HTTP错误: {e}")
    except Exception as e:
        raise Exception(f"检查更新失败: {str(e)}")


def check_update(software_name, current_version, base_url="https://unlockoko.com"):
    """检查并处理更新 - 简化版本，避免复杂的线程问题"""
    try:
        # 检查更新
        update_info = check_update_api(software_name, current_version, base_url)

        if not update_info:
            messagebox.showinfo("更新检查", "当前已是最新版本")
            return False

        # 验证更新信息
        if not update_info.get('version') or not update_info.get('download_url'):
            messagebox.showerror("更新检查", "服务器返回的更新信息不完整")
            return False

        # 创建GUI
        gui = UpdaterGUI(software_name, current_version)

        # 询问是否下载
        if not gui.show_update_dialog(update_info):
            return False

        # 显示进度窗口
        progress_window = gui.show_progress_window()

        # 简化：在新线程中下载，线程安全的GUI操作
        def download_thread():
            try:
                file_path = download_file(update_info['download_url'], gui, base_url)

                # 线程安全的关闭窗口和处理结果
                def handle_success():
                    gui.close()
                    if file_path and not gui.cancelled:
                        FileReplacer.install_update(file_path, software_name)

                # 在主线程中执行GUI操作
                progress_window.after(0, handle_success)

            except Exception as e:
                # 线程安全的错误处理
                def handle_error():
                    gui.close()
                    messagebox.showerror("下载失败", f"下载时发生错误：\n{str(e)}")

                progress_window.after(0, handle_error)

        thread = threading.Thread(target=download_thread, daemon=True)
        thread.start()

        # 运行GUI
        progress_window.mainloop()

        return True

    except Exception as e:
        messagebox.showerror("更新检查失败", str(e))
        return False


if __name__ == "__main__":
    # 测试
    check_update("imei_tool", "1.0.0")
