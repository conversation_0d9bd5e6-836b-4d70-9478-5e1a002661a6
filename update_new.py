"""
简洁优雅的跨平台更新模块 - 全新重构版本

特性：
- 跨平台支持 (Windows/Linux/Mac)
- GUI进度条显示 (使用系统原生对话框)
- 自动ZIP解压安装
- 避免tkinter冲突 (不使用tkinter)
- 线程安全设计
- 简洁优雅的API

使用方法：
    from update_new import UpdateManager
    
    # 手动检查更新
    updater = UpdateManager("your_software", "1.0.0")
    updater.check_once()
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import logging
import shutil
import zipfile
import platform
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

# 日志配置
logger = logging.getLogger('UpdateManager')
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

# 平台检测
IS_WINDOWS = platform.system().lower() == 'windows'
IS_MACOS = platform.system().lower() == 'darwin'
IS_LINUX = platform.system().lower() == 'linux'


class ProgressDisplay:
    """跨平台进度显示器 - 使用控制台进度条"""
    
    def __init__(self, title: str = "下载进度"):
        self.title = title
        self.last_percentage = -1
        
    def update(self, percentage: float, status: str = ""):
        """更新进度显示"""
        percentage = max(0, min(100, percentage))
        
        # 只在进度变化时更新显示
        if abs(percentage - self.last_percentage) < 1:
            return
            
        self.last_percentage = percentage
        
        # 创建进度条
        bar_length = 50
        filled_length = int(bar_length * percentage / 100)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        # 清除当前行并显示新进度
        print(f'\r{self.title}: [{bar}] {percentage:.1f}% {status}', end='', flush=True)
        
        if percentage >= 100:
            print()  # 完成时换行


class SystemDialog:
    """跨平台系统对话框"""
    
    @staticmethod
    def show_info(title: str, message: str) -> None:
        """显示信息对话框"""
        try:
            if IS_WINDOWS:
                # Windows使用msg命令
                subprocess.run(['msg', '*', f'{title}\n\n{message}'], 
                             creationflags=subprocess.CREATE_NO_WINDOW)
            elif IS_MACOS:
                # macOS使用osascript
                script = f'display dialog "{message}" with title "{title}" buttons {{"确定"}} default button "确定"'
                subprocess.run(['osascript', '-e', script])
            else:
                # Linux尝试使用notify-send或zenity
                try:
                    subprocess.run(['notify-send', title, message])
                except FileNotFoundError:
                    try:
                        subprocess.run(['zenity', '--info', f'--title={title}', f'--text={message}'])
                    except FileNotFoundError:
                        print(f"\n{title}\n{message}")
        except Exception:
            # 回退到控制台输出
            print(f"\n{title}\n{message}")
    
    @staticmethod
    def ask_yes_no(title: str, message: str) -> bool:
        """显示是/否对话框"""
        try:
            if IS_WINDOWS:
                # Windows使用PowerShell
                script = f'[System.Windows.Forms.MessageBox]::Show("{message}", "{title}", "YesNo", "Question")'
                result = subprocess.run(['powershell', '-Command', 
                                       'Add-Type -AssemblyName System.Windows.Forms; ' + script], 
                                      capture_output=True, text=True)
                return 'Yes' in result.stdout
            elif IS_MACOS:
                # macOS使用osascript
                script = f'display dialog "{message}" with title "{title}" buttons {{"否", "是"}} default button "是"'
                result = subprocess.run(['osascript', '-e', script], capture_output=True, text=True)
                return 'button returned:是' in result.stdout
            else:
                # Linux使用zenity
                try:
                    result = subprocess.run(['zenity', '--question', f'--title={title}', f'--text={message}'])
                    return result.returncode == 0
                except FileNotFoundError:
                    # 回退到控制台输入
                    response = input(f"\n{title}\n{message}\n是否继续？(y/n): ").strip().lower()
                    return response in ['y', 'yes', '是']
        except Exception:
            # 回退到控制台输入
            response = input(f"\n{title}\n{message}\n是否继续？(y/n): ").strip().lower()
            return response in ['y', 'yes', '是']


class UpdateManager:
    """简洁优雅的更新管理器"""
    
    def __init__(self, software_name: str, current_version: str, 
                 base_url: str = "https://unlockoko.com"):
        """初始化更新管理器
        
        Args:
            software_name: 软件名称
            current_version: 当前版本号
            base_url: 服务器地址
        """
        self.software_name = software_name.lower()
        self.current_version = current_version
        self.base_url = base_url.rstrip('/')
        self.update_info: Optional[Dict[str, Any]] = None
        self.running = True
        self._background_thread: Optional[threading.Thread] = None
        
        logger.info(f"初始化更新管理器: {software_name} v{current_version}")
    
    def check_update(self) -> bool:
        """检查是否有更新"""
        try:
            url = f"{self.base_url}/api/check_update"
            params = {
                "software_name": self.software_name,
                "current_version": self.current_version
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if data.get("code") == "100":
                update_data = data.get("data", {})
                if update_data.get("need_update", False):
                    self.update_info = {
                        "version": update_data.get("version", ""),
                        "download_url": update_data.get("download_url", ""),
                        "release_notes": update_data.get("release_notes", "")
                    }
                    logger.info(f"发现新版本: {self.update_info['version']}")
                    return True
            return False
            
        except Exception as e:
            logger.error(f"检查更新失败: {e}")
            return False
    
    def check_once(self) -> bool:
        """手动检查一次更新"""
        if not self.check_update():
            SystemDialog.show_info("更新检查", "当前已是最新版本")
            return False
        
        # 显示更新通知
        message = (f"发现新版本 {self.update_info['version']}\n"
                  f"当前版本: {self.current_version}\n\n"
                  f"更新说明:\n{self.update_info['release_notes']}\n\n"
                  f"是否立即下载更新？")
        
        if SystemDialog.ask_yes_no("发现新版本", message):
            self._download_update()
        
        return True
    
    def start_background_check(self, interval: int = 3600) -> None:
        """启动后台检查
        
        Args:
            interval: 检查间隔(秒)，默认1小时
        """
        if self._background_thread and self._background_thread.is_alive():
            return
            
        self.running = True
        self._background_thread = threading.Thread(
            target=self._background_check_loop, 
            args=(interval,), 
            daemon=True
        )
        self._background_thread.start()
        logger.info("启动后台更新检查")
    
    def stop(self) -> None:
        """停止后台检查"""
        self.running = False
        if self._background_thread:
            self._background_thread.join(timeout=1)
        logger.info("停止更新管理器")
    
    def _background_check_loop(self, interval: int) -> None:
        """后台检查循环"""
        while self.running:
            try:
                if self.check_update():
                    message = (f"发现新版本 {self.update_info['version']}\n"
                              f"当前版本: {self.current_version}\n\n"
                              f"是否立即下载更新？")
                    
                    if SystemDialog.ask_yes_no("发现新版本", message):
                        self._download_update()
                        break  # 下载完成后退出循环
                        
            except Exception as e:
                logger.error(f"后台检查失败: {e}")
            
            # 可中断的等待
            for _ in range(interval):
                if not self.running:
                    return
                time.sleep(1)
    
    def _download_update(self) -> None:
        """下载更新"""
        try:
            download_url = f"{self.base_url}{self.update_info['download_url']}"
            logger.info(f"开始下载: {download_url}")
            
            # 创建进度显示器
            progress = ProgressDisplay(f"下载 {self.update_info['version']}")
            
            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()
            
            # 获取文件名和大小
            filename = self._get_filename(response)
            total_size = int(response.headers.get('content-length', 0))
            
            # 确定保存路径
            app_dir = self._get_app_directory()
            save_path = app_dir / filename
            
            # 下载文件
            downloaded_size = 0
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            percentage = (downloaded_size / total_size) * 100
                            status = f"({downloaded_size/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB)"
                            progress.update(percentage, status)
            
            logger.info(f"下载完成: {save_path}")
            
            # 处理下载完成
            self._handle_download_complete(save_path)
            
        except Exception as e:
            logger.error(f"下载失败: {e}")
            SystemDialog.show_info("下载失败", f"下载更新时发生错误:\n{e}")
    
    def _handle_download_complete(self, file_path: Path) -> None:
        """处理下载完成"""
        try:
            if file_path.suffix.lower() == '.zip':
                # ZIP文件自动解压
                self._install_zip(file_path)
                SystemDialog.show_info("更新完成", 
                                     f"ZIP包更新安装成功！\n版本: {self.update_info['version']}")
            else:
                # 其他文件提示手动安装
                SystemDialog.show_info("下载完成", 
                                     f"文件下载完成: {file_path.name}\n请手动安装更新。")
                
        except Exception as e:
            logger.error(f"处理下载完成失败: {e}")
            SystemDialog.show_info("安装失败", f"安装更新时发生错误:\n{e}")
    
    def _install_zip(self, zip_path: Path) -> None:
        """安装ZIP包"""
        app_dir = self._get_app_directory()
        temp_dir = app_dir / f"temp_extract_{int(time.time())}"
        
        try:
            temp_dir.mkdir(exist_ok=True)
            
            # 解压ZIP文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 复制文件到应用目录
            for item in temp_dir.rglob('*'):
                if item.is_file():
                    rel_path = item.relative_to(temp_dir)
                    target_path = app_dir / rel_path
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_path)
                    
                    # 设置可执行权限
                    if not IS_WINDOWS and target_path.suffix.lower() in {'.exe', '.sh', '.py'}:
                        target_path.chmod(0o755)
            
            logger.info("ZIP包安装完成")
            
        finally:
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
            zip_path.unlink(missing_ok=True)
    
    def _get_filename(self, response) -> str:
        """从响应中获取文件名"""
        content_disposition = response.headers.get('content-disposition', '')
        if 'filename=' in content_disposition:
            filename = content_disposition.split('filename=')[1].strip('"\'')
            return filename
        return os.path.basename(response.url.split('?')[0])
    
    def _get_app_directory(self) -> Path:
        """获取应用程序目录"""
        try:
            if getattr(sys, 'frozen', False):
                return Path(sys.executable).parent.resolve()
            else:
                return Path(sys.argv[0]).parent.resolve()
        except Exception:
            return Path.cwd()


# 便利函数
def check_update_once(software_name: str, current_version: str, 
                     base_url: str = "https://unlockoko.com") -> bool:
    """手动检查一次更新"""
    updater = UpdateManager(software_name, current_version, base_url)
    return updater.check_once()


def start_update_checker(software_name: str, current_version: str, 
                        base_url: str = "https://unlockoko.com", 
                        interval: int = 3600) -> UpdateManager:
    """启动后台更新检查"""
    updater = UpdateManager(software_name, current_version, base_url)
    updater.start_background_check(interval)
    return updater


if __name__ == "__main__":
    # 示例用法
    print("简洁优雅的跨平台更新模块")
    print("=" * 40)
    
    # 手动检查更新
    updater = UpdateManager("imei_tool", "1.0.0")
    updater.check_once()
