# 简洁优雅的GUI更新模块

## 🌟 特性

- ✅ **GUI进度条显示** - 美观的图形界面，实时显示下载进度
- ✅ **完善的文件替换逻辑** - 智能处理ZIP、EXE等不同文件类型
- ✅ **跨平台支持** - Windows/Linux/Mac全平台兼容
- ✅ **避免tkinter冲突** - 使用独立tkinter实例，不影响主程序
- ✅ **线程安全设计** - 下载在独立线程，GUI响应流畅
- ✅ **简洁优雅的API** - 只需一行代码即可集成

## 🚀 快速开始

### 基本使用

```python
from updater import check_update

# 只需一行代码
check_update("your_software", "1.0.0")
```

### 集成到现有项目

```python
import tkinter as tk
from updater import check_update

# 您的主程序可以正常使用tkinter
root = tk.Tk()
root.title("我的程序")

# 在程序启动时检查更新 - 不会冲突
check_update("my_app", "1.0.0")

# 或者在菜单中添加更新功能
def menu_check_update():
    check_update("my_app", "1.0.0")

root.mainloop()
```

## 📋 功能详解

### 1. GUI界面展示

- **更新通知对话框** - 显示版本信息和更新说明
- **进度条窗口** - 实时显示下载进度和文件大小
- **取消下载功能** - 用户可随时取消下载
- **窗口置顶显示** - 确保用户能看到进度

### 2. 智能文件处理

#### ZIP文件
- 自动解压到程序目录
- 覆盖旧文件
- 设置可执行权限（Linux/Mac）
- 自动清理临时文件

#### EXE文件（同名）
- 检测是否为打包的exe程序
- 创建替换脚本（Windows用bat，Linux用sh）
- 安全的延迟替换流程
- 自动重启程序

#### 其他文件
- 下载到程序目录
- 提示用户手动处理

### 3. 路径处理

- **开发环境** - 使用脚本所在目录
- **打包后** - 使用exe所在目录
- **PyInstaller** - 正确处理`_MEIPASS`
- **备用方案** - 使用当前工作目录

## 🔧 技术细节

### 避免tkinter冲突

```python
def show_update_dialog(self, update_info):
    # 创建独立的根窗口避免冲突
    dialog_root = tk.Tk()
    dialog_root.withdraw()  # 隐藏主窗口
    
    try:
        result = messagebox.askyesno("发现新版本", message, parent=dialog_root)
        return result
    finally:
        dialog_root.destroy()  # 确保清理
```

### 线程安全设计

```python
def check_update(software_name, current_version):
    # 主线程：显示对话框
    if not gui.show_update_dialog(update_info):
        return False
    
    # 主线程：创建进度窗口
    progress_window = gui.show_progress_window()
    
    # 子线程：下载文件
    def download_thread():
        file_path = download_file(update_info['download_url'], gui)
        # 下载完成后处理
    
    threading.Thread(target=download_thread, daemon=True).start()
    progress_window.mainloop()  # 主线程运行GUI
```

### 文件替换脚本

#### Windows (bat)
```batch
@echo off
title 软件名 - 正在更新
echo 正在更新，请稍候...
timeout /t 2 /nobreak > nul
copy /Y "new.exe" "current.exe"
del "new.exe"
start "" "current.exe"
del "%~f0"
```

#### Linux/Mac (sh)
```bash
#!/bin/bash
echo "软件名 - 正在更新..."
sleep 2
cp "new.exe" "current.exe"
chmod +x "current.exe"
rm "new.exe"
nohup "current.exe" > /dev/null 2>&1 &
rm "$0"
```

## 🧪 测试

### 测试tkinter冲突

```bash
python test_conflict.py
```

选择模式2，测试主程序GUI与更新模块的兼容性。

### 测试基本功能

```bash
python updater.py
```

直接测试更新功能。

## 📦 服务器端配置

确保您的服务器提供以下API：

### 检查更新API
```
GET /api/check_update?software_name=your_app&current_version=1.0.0

响应：
{
    "code": "100",
    "data": {
        "need_update": true,
        "version": "1.1.0",
        "download_url": "/api/download/your_app",
        "release_notes": "修复了一些问题"
    }
}
```

### 下载文件API
```
GET /api/download/your_app

返回文件流，支持：
- .zip 文件（自动解压安装）
- .exe 文件（智能替换）
- 其他文件（手动安装）
```

## 🎨 界面展示

- **窗口尺寸**: 500x180像素
- **字体**: Microsoft YaHei（中文友好）
- **进度条**: 450像素宽，带视觉padding
- **布局**: 25像素边距，15像素间距
- **置顶显示**: 确保用户能看到

## ⚠️ 注意事项

1. **依赖项**: 需要 `requests` 库
2. **权限**: 更新exe文件可能需要管理员权限
3. **防火墙**: 确保网络连接正常
4. **路径**: 确保程序有写入权限

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ GUI进度条显示
- ✅ 完善的文件替换逻辑
- ✅ 跨平台支持
- ✅ 避免tkinter冲突
- ✅ 线程安全设计
- ✅ 简洁优雅的API
- ✅ 美化界面和布局
- ✅ 修复路径处理问题

## 📄 许可证

MIT License - 可自由使用和修改
