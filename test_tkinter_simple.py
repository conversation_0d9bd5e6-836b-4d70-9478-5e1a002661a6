#!/usr/bin/env python3
"""
简单的tkinter冲突测试

模拟主程序使用tkinter，然后调用更新模块
"""

import tkinter as tk
from tkinter import ttk
import threading
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from updater import check_update

class SimpleApp:
    """简单的主程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("主程序")
        self.root.geometry("300x200")
        
        frame = ttk.Frame(self.root, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="主程序界面", font=('Arial', 12, 'bold')).pack(pady=10)
        
        ttk.Button(frame, text="检查更新", command=self.check_update).pack(pady=10)
        ttk.Button(frame, text="其他功能", command=self.other_function).pack(pady=5)
        
        self.status = ttk.Label(frame, text="程序运行中...")
        self.status.pack(pady=10)
    
    def check_update(self):
        """检查更新"""
        self.status.config(text="正在检查更新...")
        
        def update_thread():
            try:
                check_update("test_app", "1.0")
                self.root.after(0, lambda: self.status.config(text="更新检查完成"))
            except Exception as e:
                self.root.after(0, lambda: self.status.config(text=f"更新失败: {e}"))
        
        threading.Thread(target=update_thread, daemon=True).start()
    
    def other_function(self):
        """其他功能"""
        self.status.config(text="执行其他功能...")
        self.root.after(2000, lambda: self.status.config(text="程序运行中..."))
    
    def run(self):
        """运行程序"""
        print("主程序启动，测试tkinter冲突...")
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleApp()
    app.run()
