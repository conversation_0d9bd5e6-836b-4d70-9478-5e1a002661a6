#!/usr/bin/env python3
"""
专门测试进度条功能的脚本

用于验证进度条是否能正常更新
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import sys
import os

# 添加当前目录到路径，以便导入update模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from update import ProgressUpdater

class ProgressBarTester:
    """进度条测试器"""
    
    def __init__(self):
        self.window = None
        self.progress_updater = None
        self.test_running = False
        
    def create_test_window(self):
        """创建测试窗口"""
        self.window = tk.Tk()
        self.window.title("进度条测试 - 修复验证")
        self.window.geometry("500x200")
        self.window.resizable(False, False)
        
        # 居中显示
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 500) // 2
        y = (screen_height - 200) // 2
        self.window.geometry(f"500x200+{x}+{y}")
        
        # 创建界面
        frame = ttk.Frame(self.window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(frame, text="进度条测试 - 验证修复效果", 
                 font=('Arial', 12, 'bold')).pack(pady=(0, 15))
        
        # 进度条 - 使用与update.py相同的配置
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            frame,
            variable=progress_var,
            length=450,
            mode='determinate',  # 确定进度模式
            maximum=100.0        # 设置最大值为100
        )
        progress_bar.pack(pady=(0, 15))
        
        # 状态标签
        status_label = ttk.Label(frame, text="准备开始测试...")
        status_label.pack(pady=(0, 15))
        
        # 创建进度更新器
        self.progress_updater = ProgressUpdater(self.window, progress_var, status_label)
        
        # 按钮
        button_frame = ttk.Frame(frame)
        button_frame.pack()
        
        ttk.Button(button_frame, text="开始测试", command=self.start_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="停止测试", command=self.stop_test).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="关闭", command=self.close_window).pack(side=tk.LEFT)
        
        # 设置关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.close_window)
        
    def start_test(self):
        """开始测试"""
        if self.test_running:
            print("测试已在运行中")
            return
            
        self.test_running = True
        print("开始进度条测试...")
        
        # 在独立线程中运行测试
        test_thread = threading.Thread(target=self._run_test, daemon=True)
        test_thread.start()
        
    def stop_test(self):
        """停止测试"""
        self.test_running = False
        print("停止测试")
        
    def close_window(self):
        """关闭窗口"""
        self.test_running = False
        if self.window:
            self.window.destroy()
            
    def _run_test(self):
        """运行测试的核心逻辑"""
        try:
            print("测试1: 基本进度更新")
            for i in range(101):
                if not self.test_running:
                    break
                    
                percentage = float(i)
                status_text = f"测试进度: {i}%"
                
                if self.progress_updater:
                    self.progress_updater.update_progress(percentage, status_text)
                
                print(f"设置进度: {percentage}%")
                time.sleep(0.05)  # 50ms间隔
                
            if not self.test_running:
                return
                
            print("\n测试2: 快速更新测试")
            for i in range(0, 101, 5):  # 每次跳5%
                if not self.test_running:
                    break
                    
                percentage = float(i)
                status_text = f"快速测试: {i}%"
                
                if self.progress_updater:
                    self.progress_updater.update_progress(percentage, status_text)
                
                print(f"快速设置进度: {percentage}%")
                time.sleep(0.1)  # 100ms间隔
                
            if not self.test_running:
                return
                
            print("\n测试3: 模拟下载场景")
            total_size = 10 * 1024 * 1024  # 模拟10MB文件
            downloaded = 0
            chunk_size = 64 * 1024  # 64KB块
            
            while downloaded < total_size and self.test_running:
                downloaded += chunk_size
                if downloaded > total_size:
                    downloaded = total_size
                    
                percentage = (downloaded / total_size) * 100
                status_text = f"{downloaded/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB ({percentage:.1f}%)"
                
                if self.progress_updater:
                    self.progress_updater.update_progress(percentage, status_text)
                
                print(f"模拟下载: {percentage:.1f}%")
                time.sleep(0.02)  # 20ms间隔，模拟真实下载
                
            if self.test_running and self.progress_updater:
                self.progress_updater.update_progress(100.0, "测试完成！")
                print("\n✅ 所有测试完成！")
                
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
        finally:
            self.test_running = False

def main():
    """主函数"""
    print("=" * 60)
    print("进度条功能测试 - 验证修复效果")
    print("=" * 60)
    print("这个测试将验证进度条是否能正常更新")
    print("请观察进度条是否随着百分比实时移动")
    print("=" * 60)
    
    try:
        # 创建测试器
        tester = ProgressBarTester()
        tester.create_test_window()
        
        print("测试窗口已创建，点击'开始测试'按钮开始")
        print("观察要点：")
        print("1. 进度条是否随百分比实时移动")
        print("2. 状态文字是否正常更新")
        print("3. 界面是否流畅无卡顿")
        
        # 运行GUI
        tester.window.mainloop()
        
        print("测试结束")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
