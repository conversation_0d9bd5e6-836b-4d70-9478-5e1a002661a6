#!/usr/bin/env python3
"""
简单的进度条测试 - 直接验证问题
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

def test_basic_progressbar():
    """测试基本的进度条功能"""
    print("创建基本进度条测试...")
    
    root = tk.Tk()
    root.title("基本进度条测试")
    root.geometry("400x150")
    
    # 居中显示
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 400) // 2
    y = (screen_height - 150) // 2
    root.geometry(f"400x150+{x}+{y}")
    
    frame = ttk.Frame(root, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(frame, text="基本进度条测试", font=('Arial', 12, 'bold')).pack(pady=(0, 10))
    
    # 方法1：直接设置value
    print("方法1：直接设置value属性")
    progress1 = ttk.Progressbar(frame, length=300, mode='determinate', maximum=100)
    progress1.pack(pady=5)
    
    # 方法2：使用变量绑定
    print("方法2：使用变量绑定")
    progress_var = tk.DoubleVar()
    progress2 = ttk.Progressbar(frame, length=300, mode='determinate', maximum=100, variable=progress_var)
    progress2.pack(pady=5)
    
    status_label = ttk.Label(frame, text="准备测试...")
    status_label.pack(pady=5)
    
    def run_test():
        """运行测试"""
        print("开始测试进度条更新...")
        
        for i in range(101):
            # 方法1：直接设置
            progress1['value'] = i
            
            # 方法2：通过变量
            progress_var.set(i)
            
            # 更新状态
            status_label.config(text=f"进度: {i}%")
            
            # 强制刷新
            root.update_idletasks()
            root.update()
            
            print(f"设置进度: {i}%")
            time.sleep(0.05)
            
        status_label.config(text="测试完成！")
        print("测试完成！")
    
    # 启动测试线程
    test_thread = threading.Thread(target=run_test, daemon=True)
    test_thread.start()
    
    root.mainloop()

def test_update_module_style():
    """测试update模块的样式"""
    print("创建update模块样式测试...")
    
    root = tk.Tk()
    root.title("Update模块样式测试")
    root.geometry("500x160")
    root.attributes('-topmost', True)
    
    # 居中显示
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 500) // 2
    y = (screen_height - 160) // 2
    root.geometry(f"500x160+{x}+{y}")
    
    frame = ttk.Frame(root, padding="20")
    frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    ttk.Label(frame, text="正在下载 测试版本", font=('Arial', 10, 'bold')).pack(pady=(0, 10))
    
    # 进度条 - 完全按照update.py的配置
    progress_var = tk.DoubleVar()
    progress_bar = ttk.Progressbar(
        frame,
        variable=progress_var,
        length=450,
        mode='determinate',
        maximum=100.0
    )
    progress_bar.pack(pady=(0, 10))
    
    # 状态标签
    status_label = ttk.Label(frame, text="准备下载...")
    status_label.pack(pady=(0, 10))
    
    # 取消按钮
    ttk.Button(frame, text="取消下载", command=root.destroy).pack()
    
    def simulate_download():
        """模拟下载过程"""
        print("开始模拟下载...")
        
        # 连接阶段
        progress_var.set(0)
        status_label.config(text="连接服务器...")
        root.update_idletasks()
        root.update()
        time.sleep(1)
        
        # 下载阶段
        total_size = 10 * 1024 * 1024  # 10MB
        downloaded = 0
        chunk_size = 64 * 1024  # 64KB
        
        while downloaded < total_size:
            downloaded += chunk_size
            if downloaded > total_size:
                downloaded = total_size
                
            percentage = (downloaded / total_size) * 100
            status_text = f"{downloaded/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB ({percentage:.1f}%)"
            
            # 关键：确保百分比在正确范围内
            safe_percentage = max(0.0, min(100.0, percentage))
            
            # 更新进度条
            progress_var.set(safe_percentage)
            status_label.config(text=status_text)
            
            # 强制刷新GUI
            root.update_idletasks()
            root.update()
            
            print(f"下载进度: {safe_percentage:.1f}% - {status_text}")
            time.sleep(0.02)  # 20ms
            
        status_label.config(text="下载完成！")
        print("下载模拟完成！")
    
    # 启动模拟下载
    download_thread = threading.Thread(target=simulate_download, daemon=True)
    download_thread.start()
    
    root.mainloop()

def main():
    """主函数"""
    print("=" * 60)
    print("简单进度条测试")
    print("=" * 60)
    
    choice = input("选择测试类型:\n1. 基本进度条测试\n2. Update模块样式测试\n请输入 (1 或 2): ").strip()
    
    if choice == "1":
        test_basic_progressbar()
    elif choice == "2":
        test_update_module_style()
    else:
        print("无效选择，运行基本测试...")
        test_basic_progressbar()

if __name__ == "__main__":
    main()
