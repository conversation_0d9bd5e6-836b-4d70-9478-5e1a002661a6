#!/usr/bin/env python3
"""
测试tkinter冲突问题

模拟一个使用tkinter的主程序，然后调用更新模块
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from updater import check_update

class MainApp:
    """主程序 - 使用tkinter"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("主程序 - 测试tkinter冲突")
        self.root.geometry("400x300")
        
        # 创建界面
        frame = ttk.Frame(self.root, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="主程序界面", font=('Arial', 14, 'bold')).pack(pady=20)
        
        ttk.Label(frame, text="这是一个使用tkinter的主程序").pack(pady=10)
        
        # 检查更新按钮
        ttk.Button(frame, text="检查更新", command=self.check_update_clicked).pack(pady=10)
        
        # 其他功能按钮
        ttk.Button(frame, text="功能1", command=self.function1).pack(pady=5)
        ttk.Button(frame, text="功能2", command=self.function2).pack(pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(frame, text="程序运行中...")
        self.status_label.pack(pady=20)
        
        # 启动状态更新
        self.update_status()
    
    def check_update_clicked(self):
        """检查更新按钮点击"""
        self.status_label.config(text="正在检查更新...")
        
        # 在新线程中检查更新，避免阻塞主界面
        def check_thread():
            try:
                check_update("test_app", "1.0.0")
                self.root.after(0, lambda: self.status_label.config(text="更新检查完成"))
            except Exception as e:
                self.root.after(0, lambda: self.status_label.config(text=f"更新检查失败: {e}"))
        
        threading.Thread(target=check_thread, daemon=True).start()
    
    def function1(self):
        """功能1"""
        self.status_label.config(text="执行功能1...")
        self.root.after(2000, lambda: self.status_label.config(text="功能1执行完成"))
    
    def function2(self):
        """功能2"""
        self.status_label.config(text="执行功能2...")
        self.root.after(2000, lambda: self.status_label.config(text="功能2执行完成"))
    
    def update_status(self):
        """更新状态显示"""
        current_time = time.strftime("%H:%M:%S")
        if "程序运行中" in self.status_label.cget("text"):
            self.status_label.config(text=f"程序运行中... {current_time}")
        
        # 每秒更新一次
        self.root.after(1000, self.update_status)
    
    def run(self):
        """运行主程序"""
        print("主程序启动...")
        print("测试场景：主程序使用tkinter，然后调用更新模块")
        print("观察是否会发生tkinter冲突")
        print("=" * 50)
        
        self.root.mainloop()

def test_simple_integration():
    """测试简单集成"""
    print("测试1: 简单集成测试")
    print("直接调用更新模块...")
    
    try:
        check_update("test_simple", "1.0.0")
        print("✓ 简单集成测试成功")
    except Exception as e:
        print(f"❌ 简单集成测试失败: {e}")

def main():
    """主函数"""
    print("🔧 tkinter冲突测试")
    print("=" * 50)
    
    choice = input("选择测试模式:\n1. 简单集成测试\n2. 主程序GUI测试\n请输入 (1 或 2): ").strip()
    
    if choice == "1":
        test_simple_integration()
    elif choice == "2":
        app = MainApp()
        app.run()
    else:
        print("无效选择，运行简单测试...")
        test_simple_integration()

if __name__ == "__main__":
    main()
