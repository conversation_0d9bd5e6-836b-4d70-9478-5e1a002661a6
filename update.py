"""
专业级跨平台更新模块 - 简洁优雅版本

使用方法：
    from update import UpdateManager

    # 手动检查更新
    updater = UpdateManager("your_software", "1.0.0")
    updater.check_once()

    # 后台自动检查
    updater = UpdateManager("your_software", "1.0.0")
    updater.start_background_check()

特性：
- 跨平台支持 (Windows/Linux/Mac)
- GUI进度条显示
- 自动ZIP解压安装
- 避免tkinter冲突
- 线程安全设计
- 简洁优雅的API
"""

import os
import sys
import time
import threading
import subprocess
import tempfile
import requests
import logging
import atexit
import re
import shutil
import zipfile
import platform
from pathlib import Path
from typing import Optional, Dict, Any, Callable
from datetime import datetime, timedelta

# 优化的日志配置 - 不保存到文件
_logger = logging.getLogger('UpdateManager')
_logger.setLevel(logging.INFO)
if not _logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    _logger.addHandler(handler)

# 平台检测
IS_WINDOWS = platform.system().lower() == 'windows'

# 全局状态管理 - 避免多实例冲突
_update_lock = threading.Lock()
_active_updater = None


def _get_filename_from_cd(content_disposition: str) -> Optional[str]:
    """从Content-Disposition头中提取文件名"""
    if not content_disposition:
        return None
    fname = re.findall(r'filename=["\'](.*?)["\']|filename=([^;]+)', content_disposition)
    if fname:
        for groups in fname:
            for group in groups:
                if group:
                    return group.strip()
    return None


def _get_app_directory() -> Path:
    """获取应用程序目录"""
    try:
        if getattr(sys, 'frozen', False):
            return Path(sys.executable).parent.resolve()
        else:
            return Path(sys.argv[0]).parent.resolve()
    except Exception:
        return Path.cwd()


def _is_zip_file(filename: str) -> bool:
    """检查是否为ZIP文件"""
    return filename.lower().endswith('.zip')


def _is_same_file(filename: str) -> bool:
    """检查是否为同名文件"""
    try:
        current_name = Path(sys.argv[0]).name
        return filename.lower() == current_name.lower()
    except Exception:
        return False


def _is_frozen() -> bool:
    """检查是否为打包后的可执行文件"""
    return getattr(sys, 'frozen', False)


def _validate_zip_path(zip_path: str) -> bool:
    """验证ZIP路径安全性，防止路径遍历攻击"""
    try:
        normalized = os.path.normpath(zip_path)
        dangerous_patterns = ['..', '/', '\\', ':']
        if any(pattern in normalized for pattern in dangerous_patterns):
            return False
        if os.path.isabs(normalized):
            return False
        return True
    except Exception:
        return False


def _safe_path_quote(path: Path) -> str:
    """安全的路径引用，处理空格和特殊字符"""
    path_str = str(path)
    if IS_WINDOWS:
        return f'"{path_str}"'
    else:
        return path_str.replace(' ', '\\ ').replace('(', '\\(').replace(')', '\\)')


def _try_delayed_replace(software_name: str = "软件更新") -> None:
    """检查并执行延迟替换"""
    try:
        app_dir = _get_app_directory()
        current_exe = Path(sys.argv[0])
        new_file = app_dir / f"{current_exe.name}.new"

        if new_file.exists() and _is_frozen():
            _logger.info("检测到延迟升级文件，准备替换...")

            if new_file.stat().st_size == 0:
                new_file.unlink(missing_ok=True)
                _logger.warning("删除无效的延迟升级文件")
                return

            _create_replace_script(current_exe, new_file, software_name)

    except Exception as e:
        _logger.error(f"延迟升级失败: {str(e)}")


def _create_replace_script(current_exe: Path, new_file: Path, software_name: str) -> None:
    """创建替换脚本"""
    temp_dir = Path(tempfile.mkdtemp())

    try:
        if IS_WINDOWS:
            script_file = temp_dir / "replace.bat"
            current_quoted = _safe_path_quote(current_exe)
            new_quoted = _safe_path_quote(new_file)
            temp_quoted = _safe_path_quote(temp_dir)

            with open(script_file, "w", encoding="utf-8") as f:
                f.write("@echo off\n")
                f.write(f"title {software_name} - 正在更新\n")
                f.write("echo 正在更新，请稍候...\n")
                f.write("timeout /t 3 /nobreak > nul\n")
                f.write(f"copy /Y {new_quoted} {current_quoted}\n")
                f.write(f"del /f /q {new_quoted}\n")
                f.write(f"start \"\" {current_quoted}\n")
                f.write(f"rmdir /S /Q {temp_quoted}\n")

            subprocess.Popen(["cmd", "/c", str(script_file)],
                           creationflags=subprocess.CREATE_NO_WINDOW)
        else:
            script_file = temp_dir / "replace.sh"
            current_quoted = _safe_path_quote(current_exe)
            new_quoted = _safe_path_quote(new_file)
            temp_quoted = _safe_path_quote(temp_dir)

            with open(script_file, "w", encoding="utf-8") as f:
                f.write("#!/bin/bash\n")
                f.write(f"echo '{software_name} - 正在更新...'\n")
                f.write("sleep 3\n")
                f.write(f"cp {new_quoted} {current_quoted}\n")
                f.write(f"chmod +x {current_quoted}\n")
                f.write(f"rm -f {new_quoted}\n")
                f.write(f"nohup {current_quoted} > /dev/null 2>&1 &\n")
                f.write(f"rm -rf {temp_quoted}\n")

            script_file.chmod(0o755)
            subprocess.Popen(["/bin/bash", str(script_file)])

        _logger.info("启动替换脚本，程序即将退出")
        os._exit(0)

    except Exception as e:
        _logger.error(f"创建替换脚本失败: {str(e)}")
        shutil.rmtree(temp_dir, ignore_errors=True)


class ProgressUpdater:
    """线程安全的进度更新器 - 遵循tkinter最佳实践"""

    def __init__(self, window, progress_var, status_label):
        self.window = window
        self.progress_var = progress_var
        self.status_label = status_label
        self.last_update = 0
        self.update_interval = 0.1  # 100ms更新间隔

    def update_progress(self, percentage: float, status_text: str) -> None:
        """线程安全的进度更新 - 使用tkinter的after方法"""
        current_time = time.time()
        if current_time - self.last_update < self.update_interval:
            return

        self.last_update = current_time

        def _update_gui():
            """在主线程中执行的GUI更新函数"""
            try:
                if self.window and self.window.winfo_exists():
                    safe_percentage = max(0.0, min(100.0, percentage))

                    if self.progress_var:
                        self.progress_var.set(safe_percentage)

                    if self.status_label:
                        self.status_label.config(text=status_text)

            except Exception as e:
                _logger.debug(f"GUI更新异常: {e}")

        # tkinter最佳实践：使用after方法在主线程中执行GUI更新
        try:
            if self.window and hasattr(self.window, 'after'):
                self.window.after(0, _update_gui)
        except Exception as e:
            _logger.debug(f"调度GUI更新失败: {e}")


class UpdateManager:
    """简洁优雅的更新管理器 - 避免tkinter冲突，修复所有问题"""

    def __init__(
        self,
        software_name: str,
        current_version: str,
        base_url: str = "https://unlockoko.com",
        check_interval: int = 3600
    ):
        """初始化更新管理器

        Args:
            software_name: 软件名称
            current_version: 当前版本号
            base_url: 服务器地址
            check_interval: 检查间隔(秒)
        """
        global _active_updater

        # 防止多实例冲突
        with _update_lock:
            if _active_updater is not None:
                _logger.warning("已存在活跃的UpdateManager实例，停止旧实例")
                try:
                    _active_updater.stop()
                except Exception:
                    pass
            _active_updater = self

        self.software_name = software_name.lower()
        self.current_version = current_version
        self.base_url = base_url.rstrip('/')
        self.check_interval = check_interval

        # 状态管理
        self.update_info: Optional[Dict[str, Any]] = None
        self.running = True
        self._background_thread: Optional[threading.Thread] = None

        # GUI状态 - 避免冲突
        self._gui_window = None
        self._download_cancelled = False
        self._download_thread = None
        self._progress_updater = None

        # 用户体验优化
        self._last_check_time = None
        self._last_notification_time = None
        self._user_dismissed_version = None
        self._user_cancelled_session = False

        _logger.info(f"初始化更新管理器: {software_name} v{current_version}")
        atexit.register(self.stop)

    def start_background_check(self) -> None:
        """启动后台检查"""
        if self._background_thread is None or not self._background_thread.is_alive():
            self.running = True
            self._background_thread = threading.Thread(target=self._check_periodically, daemon=True)
            self._background_thread.start()
            _logger.info("启动后台更新检查")

    def stop(self) -> None:
        """停止所有活动"""
        self.running = False
        self._cancel_download()

        # 等待后台线程结束
        if self._background_thread and self._background_thread.is_alive():
            try:
                self._background_thread.join(timeout=1)
            except Exception:
                pass
        _logger.info("停止更新管理器")

    def _cancel_download(self) -> None:
        """安全取消下载"""
        self._download_cancelled = True

        # 等待下载线程结束
        if self._download_thread and self._download_thread.is_alive():
            try:
                self._download_thread.join(timeout=2)
            except Exception:
                pass

        # 安全关闭GUI窗口
        if self._gui_window:
            try:
                self._gui_window.quit()
                self._gui_window.destroy()
            except Exception:
                pass
            finally:
                self._gui_window = None
                self._progress_updater = None

    def _check_periodically(self) -> None:
        """定时检查循环"""
        while self.running:
            try:
                if self._should_check() and self.check_update():
                    if self._should_show_notification():
                        self._show_update_notification()
                        self._last_notification_time = datetime.now()
            except Exception as e:
                _logger.error(f"检查更新失败: {str(e)}")

            # 可中断的等待
            for _ in range(self.check_interval):
                if not self.running:
                    break
                time.sleep(1)

    def _should_check(self) -> bool:
        """判断是否应该检查"""
        if self._last_check_time:
            time_since_last = datetime.now() - self._last_check_time
            if time_since_last < timedelta(minutes=30):
                return False
        return True

    def _should_show_notification(self) -> bool:
        """判断是否应该显示通知"""
        if not self.update_info:
            return False

        # 用户在本次会话中取消过，不再通知
        if self._user_cancelled_session:
            return False

        # 用户拒绝了此版本，4小时内不再通知此版本
        if self._user_dismissed_version == self.update_info['version']:
            if self._last_notification_time:
                time_since_dismiss = datetime.now() - self._last_notification_time
                if time_since_dismiss < timedelta(hours=4):
                    return False
                else:
                    # 4小时后重置拒绝状态
                    self._user_dismissed_version = None

        # 任何版本的通知间隔至少1小时
        if self._last_notification_time:
            time_since_last = datetime.now() - self._last_notification_time
            if time_since_last < timedelta(hours=1):
                return False

        return True

    def check_update(self) -> bool:
        """检查是否有更新"""
        try:
            self._last_check_time = datetime.now()

            url = f"{self.base_url}/api/check_update"
            params = {
                "software_name": self.software_name,
                "current_version": self.current_version
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == "100":
                update_data = data.get("data", {})
                need_update = update_data.get("need_update", False)

                if need_update:
                    self.update_info = {
                        "version": update_data.get("version", ""),
                        "download_url": update_data.get("download_url", ""),
                        "release_notes": update_data.get("release_notes", "")
                    }
                    _logger.info(f"发现新版本: {self.update_info['version']}")
                    return True
                else:
                    return False
            else:
                _logger.warning(f"服务器错误: {data.get('msg', '未知错误')}")
                return False

        except Exception as e:
            _logger.error(f"检查更新失败: {str(e)}")
            return False

    def check_once(self) -> bool:
        """手动检查一次更新"""
        _try_delayed_replace(self.software_name)

        if self.check_update():
            self._show_update_notification()
            return True
        return False

    def _show_update_notification(self) -> None:
        """显示更新通知 - 避免tkinter冲突"""
        try:
            # 检查是否已有GUI窗口
            if self._gui_window:
                _logger.warning("已有GUI窗口，跳过通知")
                return

            import tkinter as tk
            from tkinter import messagebox

            # 创建独立的根窗口 - 避免冲突
            root = tk.Tk()
            root.withdraw()
            root.title(f"{self.software_name} - 更新通知")
            root.attributes('-topmost', True)

            if IS_WINDOWS:
                root.attributes('-toolwindow', True)

            try:
                # 构建消息
                message = (
                    f"发现新版本 {self.update_info['version']}\n\n"
                    f"当前版本: {self.current_version}\n\n"
                    f"更新说明:\n{self.update_info['release_notes']}\n\n"
                    "请选择操作："
                )

                # 显示对话框
                user_choice = messagebox.askyesnocancel(
                    f"{self.software_name} - 发现新版本",
                    message,
                    parent=root
                )

                # 处理用户选择
                if user_choice is True:
                    # 用户选择"是" - 立即下载
                    _logger.info("用户选择立即下载更新")
                    self._start_download()
                elif user_choice is False:
                    # 用户选择"否" - 暂不更新此版本，但继续后台检测
                    self._user_dismissed_version = self.update_info['version']
                    _logger.info(f"用户暂时拒绝更新版本: {self.update_info['version']}，4小时后再次提醒")
                elif user_choice is None:
                    # 用户选择"取消" - 本次会话不再提醒任何更新
                    self._user_cancelled_session = True
                    _logger.info("用户取消更新通知，本次会话不再提醒")

            finally:
                # 确保清理根窗口
                try:
                    root.destroy()
                except Exception:
                    pass

        except ImportError:
            _logger.warning("tkinter不可用，使用控制台通知")
            self._show_console_notification()
        except Exception as e:
            _logger.error(f"显示更新通知失败: {str(e)}")
            self._show_console_notification()

    def _show_console_notification(self) -> None:
        """控制台通知"""
        print(f"\n{'='*50}")
        print(f"🔔 {self.software_name} 更新通知")
        print(f"{'='*50}")
        print(f"发现新版本: {self.update_info['version']}")
        print(f"当前版本: {self.current_version}")
        print(f"更新说明: {self.update_info['release_notes']}")
        print(f"{'='*50}")

        try:
            print("请选择操作:")
            print("1. 立即下载")
            print("2. 暂不更新（4小时后再提醒）")
            print("3. 取消（本次会话不再提醒）")

            choice = input("请输入选择 (1-3): ").strip()
            if choice == "1":
                self._start_download()
            elif choice == "2":
                self._user_dismissed_version = self.update_info['version']
                print("已暂时跳过此版本更新，4小时后再次提醒")
            elif choice == "3":
                self._user_cancelled_session = True
                print("已取消更新通知，本次会话不再提醒")
        except (EOFError, KeyboardInterrupt):
            self._user_cancelled_session = True
            print("已取消更新通知")

    def _start_download(self) -> None:
        """启动下载 - 彻底修复进度条问题"""
        if not self.update_info or not self.update_info.get("download_url"):
            _logger.error("没有可用的更新信息")
            return

        # 检查是否已在下载
        if self._gui_window:
            _logger.warning("已有下载窗口，跳过重复下载")
            return

        self._download_cancelled = False

        try:
            import tkinter as tk
            from tkinter import ttk, messagebox

            # 创建下载窗口 - 避免冲突
            self._gui_window = tk.Tk()
            self._gui_window.title(f"{self.software_name} - 正在下载")
            self._gui_window.geometry("500x160")
            self._gui_window.resizable(False, False)
            self._gui_window.attributes('-topmost', True)

            if IS_WINDOWS:
                self._gui_window.attributes('-toolwindow', True)

            # 居中显示
            self._center_window(self._gui_window, 500, 160)

            # 创建界面
            frame = ttk.Frame(self._gui_window, padding="20")
            frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            ttk.Label(frame, text=f"正在下载 {self.update_info['version']}",
                     font=('Arial', 10, 'bold')).pack(pady=(0, 10))

            # 进度条 - 关键修复：设置正确的模式和范围
            progress_var = tk.DoubleVar()
            progress_bar = ttk.Progressbar(
                frame,
                variable=progress_var,
                length=450,
                mode='determinate',  # 确定进度模式
                maximum=100.0        # 设置最大值为100
            )
            progress_bar.pack(pady=(0, 10))

            # 状态标签
            status_label = ttk.Label(frame, text="准备下载...")
            status_label.pack(pady=(0, 10))

            # 创建专门的进度更新器 - 关键修复
            self._progress_updater = ProgressUpdater(self._gui_window, progress_var, status_label)

            # 取消按钮
            ttk.Button(frame, text="取消下载", command=self._on_cancel).pack()

            # 设置关闭事件
            self._gui_window.protocol("WM_DELETE_WINDOW", self._on_cancel)

            # 启动下载线程 - 在独立线程中下载
            self._download_thread = threading.Thread(target=self._download_file, daemon=True)
            self._download_thread.start()

            # 运行GUI - 不会阻塞，因为下载在独立线程中
            self._gui_window.mainloop()

        except ImportError:
            _logger.warning("tkinter不可用，使用控制台下载")
            self._download_console()
        except Exception as e:
            _logger.error(f"创建下载界面失败: {str(e)}")
            self._download_console()

    def _center_window(self, window, width: int, height: int) -> None:
        """居中显示窗口"""
        try:
            window.update_idletasks()
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            window.geometry(f"{width}x{height}+{x}+{y}")
        except Exception:
            window.geometry(f"{width}x{height}")

    def _on_cancel(self) -> None:
        """处理取消下载"""
        try:
            import tkinter as tk
            from tkinter import messagebox

            if self._gui_window:
                if messagebox.askokcancel("取消下载", "确定要取消下载吗？", parent=self._gui_window):
                    self._download_cancelled = True
                    self._gui_window.quit()
                    self._gui_window.destroy()
                    self._gui_window = None
                    self._progress_updater = None
                    _logger.info("用户取消了下载")
        except Exception as e:
            _logger.error(f"取消下载失败: {str(e)}")

    def _download_file(self) -> None:
        """下载文件 - 彻底修复进度条更新"""
        try:
            download_url = f"{self.base_url}{self.update_info['download_url']}"
            _logger.info(f"开始下载: {download_url}")

            # 更新状态 - 使用专门的更新器
            if self._progress_updater:
                self._progress_updater.update_progress(0, "连接服务器...")

            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()

            # 获取文件名
            content_disposition = response.headers.get('content-disposition', '')
            server_filename = _get_filename_from_cd(content_disposition)
            if not server_filename:
                server_filename = os.path.basename(download_url.split('?')[0])

            # 安全性检查
            if not server_filename or len(server_filename) > 255:
                raise Exception("无效的文件名")

            # 确定保存路径
            app_dir = _get_app_directory()
            is_zip = _is_zip_file(server_filename)
            is_same = _is_same_file(server_filename)

            if is_same and _is_frozen():
                save_path = app_dir / f"{server_filename}.new"
            else:
                save_path = app_dir / server_filename

            # 检查磁盘空间
            total_size = int(response.headers.get('content-length', 0))
            if total_size > 0:
                try:
                    free_space = shutil.disk_usage(app_dir).free
                    if total_size > free_space:
                        raise Exception(f"磁盘空间不足，需要 {total_size/1024/1024:.1f}MB")
                except Exception:
                    pass

            downloaded_size = 0

            # 更新状态
            if self._progress_updater:
                self._progress_updater.update_progress(0, "正在下载...")

            # 下载文件 - 关键修复：使用专门的进度更新器
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if self._download_cancelled:
                        f.close()
                        save_path.unlink(missing_ok=True)
                        _logger.info("下载已取消")
                        return

                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        # 彻底修复进度条更新 - 使用专门的更新器
                        if total_size > 0 and self._progress_updater:
                            percentage = (downloaded_size / total_size) * 100
                            status_text = f"{downloaded_size/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB ({percentage:.1f}%)"

                            # 关键修复：直接调用更新器，不使用lambda
                            self._progress_updater.update_progress(percentage, status_text)

            # 验证文件完整性
            if total_size > 0 and save_path.stat().st_size != total_size:
                save_path.unlink(missing_ok=True)
                raise Exception("下载文件不完整")

            _logger.info(f"下载完成: {save_path}")

            # 处理下载完成
            self._handle_download_complete_safe(save_path, is_zip, is_same)

        except Exception as e:
            error_msg = str(e)
            _logger.error(f"下载失败: {error_msg}")
            self._handle_download_error_safe(error_msg)

    def _handle_download_complete_safe(self, save_path: Path, is_zip: bool, is_same: bool) -> None:
        """安全处理下载完成"""
        def do_handle():
            try:
                import tkinter as tk
                from tkinter import messagebox

                # 安全关闭下载窗口
                if self._gui_window:
                    self._gui_window.destroy()
                    self._gui_window = None
                    self._progress_updater = None

                if is_zip:
                    try:
                        self._install_zip_safe(save_path)
                        messagebox.showinfo(
                            f"{self.software_name} - 更新完成",
                            f"ZIP包更新安装成功！\n版本: {self.update_info['version']}\n更新已立即生效。"
                        )
                    except Exception as e:
                        messagebox.showerror(
                            f"{self.software_name} - 更新失败",
                            f"ZIP包安装失败: {str(e)}"
                        )

                elif is_same and _is_frozen():
                    user_restart = messagebox.askyesno(
                        f"{self.software_name} - 更新完成",
                        f"新版本已下载完成！\n版本: {self.update_info['version']}\n\n是否立即重启以完成更新？"
                    )
                    if user_restart:
                        self._restart_for_update()
                    else:
                        messagebox.showinfo(
                            f"{self.software_name} - 更新提示",
                            "更新文件已准备就绪，下次启动时将自动完成更新。"
                        )
                else:
                    if save_path.suffix.lower() == '.exe' and not _is_frozen():
                        messagebox.showinfo(
                            f"{self.software_name} - 下载完成",
                            f"新版本exe文件下载完成！\n文件: {save_path.name}\n\n"
                            f"💡 由于您当前运行的是Python脚本，请手动运行下载的exe文件来使用新版本。"
                        )
                    else:
                        messagebox.showinfo(
                            f"{self.software_name} - 下载完成",
                            f"文件下载完成！\n文件: {save_path.name}\n\n请手动安装更新。"
                        )

            except Exception as e:
                _logger.error(f"处理下载完成失败: {str(e)}")

        # 在主线程中执行
        if self._gui_window:
            self._gui_window.after(0, do_handle)
        else:
            do_handle()

    def _handle_download_error_safe(self, error_msg: str) -> None:
        """安全处理下载错误"""
        def do_handle():
            try:
                import tkinter as tk
                from tkinter import messagebox

                if self._gui_window:
                    messagebox.showerror(
                        f"{self.software_name} - 下载失败",
                        f"下载更新时发生错误:\n\n{error_msg}\n\n请稍后重试或联系技术支持。",
                        parent=self._gui_window
                    )
                    self._gui_window.destroy()
                    self._gui_window = None
                    self._progress_updater = None

            except Exception as e:
                _logger.error(f"处理下载错误失败: {str(e)}")

        # 在主线程中执行
        if self._gui_window:
            self._gui_window.after(0, do_handle)
        else:
            do_handle()

    def _download_console(self) -> None:
        """控制台下载"""
        try:
            download_url = f"{self.base_url}{self.update_info['download_url']}"
            print(f"开始下载: {download_url}")

            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()

            content_disposition = response.headers.get('content-disposition', '')
            server_filename = _get_filename_from_cd(content_disposition)
            if not server_filename:
                server_filename = os.path.basename(download_url.split('?')[0])

            app_dir = _get_app_directory()
            save_path = app_dir / server_filename
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        if total_size > 0:
                            percentage = (downloaded_size / total_size) * 100
                            print(f"\r下载进度: {percentage:.1f}%", end='')

            print(f"\n下载完成: {save_path}")

        except Exception as e:
            print(f"下载失败: {str(e)}")

    def _install_zip_safe(self, zip_path: Path) -> None:
        """安全安装ZIP包"""
        try:
            app_dir = _get_app_directory()
            temp_dir = app_dir / f"temp_extract_{int(time.time())}"
            temp_dir.mkdir(exist_ok=True)

            try:
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    for member in zip_ref.infolist():
                        if not _validate_zip_path(member.filename):
                            raise Exception(f"检测到不安全的路径: {member.filename}")

                        if member.file_size > 100 * 1024 * 1024:
                            raise Exception(f"文件过大: {member.filename}")

                    zip_ref.extractall(temp_dir)

                for item in temp_dir.rglob('*'):
                    if item.is_file():
                        rel_path = item.relative_to(temp_dir)
                        target_path = app_dir / rel_path
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(item, target_path)

                        if not IS_WINDOWS:
                            try:
                                if target_path.suffix.lower() in {'.exe', '.sh', '.py'}:
                                    target_path.chmod(0o755)
                                else:
                                    target_path.chmod(0o644)
                            except Exception:
                                pass

                _logger.info("ZIP包安装完成")

            finally:
                shutil.rmtree(temp_dir, ignore_errors=True)
                zip_path.unlink(missing_ok=True)

        except Exception as e:
            _logger.error(f"ZIP包安装失败: {str(e)}")
            raise

    def _restart_for_update(self) -> None:
        """重启进行更新"""
        try:
            current_exe = Path(sys.argv[0])
            new_file = _get_app_directory() / f"{current_exe.name}.new"
            _create_replace_script(current_exe, new_file, self.software_name)
        except Exception as e:
            _logger.error(f"重启更新失败: {str(e)}")


# 便利函数 - 简洁的API
def check_update_once(software_name: str, current_version: str, base_url: str = "https://unlockoko.com") -> bool:
    """手动检查一次更新

    Args:
        software_name: 软件名称
        current_version: 当前版本号
        base_url: 服务器地址

    Returns:
        bool: 是否发现更新
    """
    _try_delayed_replace(software_name)
    updater = UpdateManager(software_name, current_version, base_url)
    return updater.check_once()


def start_update_checker(software_name: str, current_version: str, base_url: str = "https://unlockoko.com") -> UpdateManager:
    """启动后台更新检查

    Args:
        software_name: 软件名称
        current_version: 当前版本号
        base_url: 服务器地址

    Returns:
        UpdateManager: 更新管理器实例
    """
    _try_delayed_replace(software_name)
    updater = UpdateManager(software_name, current_version, base_url)
    updater.start_background_check()
    return updater


if __name__ == "__main__":
    # 示例用法
    print("专业级跨平台更新模块 - 简洁优雅版本")
    print("=" * 50)

    # 推荐使用方式：手动检查
    updater = UpdateManager("imei_tool", "1.0.0")
    print("更新管理器已创建")
    print("调用 updater.check_once() 来手动检查更新")
    print("调用 updater.start_background_check() 来启动后台检查")

    # 演示手动检查
    print("\n正在检查更新...")
    has_update = updater.check_once()
    if not has_update:
        print("当前已是最新版本")
