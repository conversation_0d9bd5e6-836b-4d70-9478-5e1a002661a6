#!/usr/bin/env python3
"""
测试修复后的tkinter线程问题

验证是否解决了"main thread is not in main loop"错误
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from update import UpdateManager

def test_tkinter_thread_safety():
    """测试tkinter线程安全性"""
    print("🔧 测试tkinter线程安全性修复")
    print("=" * 50)
    print("这个测试将验证是否解决了'main thread is not in main loop'错误")
    print("=" * 50)
    
    try:
        # 测试1: 基本创建和销毁
        print("\n📋 测试1: 基本创建和销毁")
        updater = UpdateManager("test_app", "1.0.0")
        print("✓ 更新管理器创建成功")
        
        updater.stop()
        print("✓ 更新管理器停止成功")
        
        # 测试2: 多次创建和销毁
        print("\n📋 测试2: 多次创建和销毁")
        for i in range(3):
            print(f"  创建第{i+1}个实例...")
            updater = UpdateManager(f"test_app_{i}", "1.0.0")
            time.sleep(0.5)
            updater.stop()
            print(f"  ✓ 第{i+1}个实例停止成功")
        
        # 测试3: 检查更新功能
        print("\n📋 测试3: 检查更新功能")
        updater = UpdateManager("imei_tool", "1.0.0")
        print("正在检查更新...")
        
        has_update = updater.check_once()
        if has_update:
            print("✓ 发现更新，GUI窗口应该正常显示和关闭")
            print("请观察窗口是否正常关闭，没有错误信息")
        else:
            print("✓ 当前已是最新版本")
        
        # 等待一段时间确保所有操作完成
        time.sleep(2)
        
        updater.stop()
        print("✓ 最终停止成功")
        
        print("\n🎉 所有测试通过！tkinter线程问题已修复！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_bar_functionality():
    """测试进度条功能"""
    print("\n" + "=" * 50)
    print("📊 测试进度条功能")
    print("=" * 50)
    
    try:
        import tkinter as tk
        from tkinter import ttk
        from update import ProgressUpdater
        import threading
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("进度条功能测试")
        root.geometry("400x120")
        
        # 居中显示
        root.update_idletasks()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - 400) // 2
        y = (screen_height - 120) // 2
        root.geometry(f"400x120+{x}+{y}")
        
        frame = ttk.Frame(root, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="进度条测试", font=('Arial', 10, 'bold')).pack(pady=(0, 10))
        
        # 进度条
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            frame,
            variable=progress_var,
            length=350,
            mode='determinate',
            maximum=100.0
        )
        progress_bar.pack(pady=(0, 10))
        
        # 状态标签
        status_label = ttk.Label(frame, text="准备测试...")
        status_label.pack()
        
        # 创建进度更新器
        progress_updater = ProgressUpdater(root, progress_var, status_label)
        
        def run_progress_test():
            """运行进度测试"""
            print("开始进度条测试...")
            
            for i in range(101):
                percentage = float(i)
                status_text = f"测试进度: {i}%"
                progress_updater.update_progress(percentage, status_text)
                time.sleep(0.02)  # 20ms间隔
                
            print("✓ 进度条测试完成")
            
            # 2秒后自动关闭
            def close_window():
                root.quit()
            root.after(2000, close_window)
        
        # 启动测试线程
        test_thread = threading.Thread(target=run_progress_test, daemon=True)
        test_thread.start()
        
        print("进度条测试窗口已创建，观察进度条是否平滑更新...")
        
        # 运行GUI
        root.mainloop()
        
        # 清理
        try:
            root.destroy()
        except:
            pass
            
        print("✓ 进度条测试完成，窗口正常关闭")
        return True
        
    except Exception as e:
        print(f"❌ 进度条测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 tkinter线程安全性修复验证")
    print("基于Python官方文档的最佳实践")
    print("https://docs.python.org/3/library/tkinter.html")
    
    # 测试1: tkinter线程安全性
    success1 = test_tkinter_thread_safety()
    
    if not success1:
        print("\n❌ tkinter线程安全性测试失败")
        return 1
    
    # 询问是否进行进度条测试
    try:
        choice = input("\n是否进行进度条功能测试？(y/n): ").strip().lower()
        if choice == 'y':
            success2 = test_progress_bar_functionality()
            if not success2:
                print("\n❌ 进度条功能测试失败")
                return 1
    except (EOFError, KeyboardInterrupt):
        print("\n跳过进度条测试")
    
    print("\n" + "=" * 50)
    print("🎉 所有修复验证通过！")
    print("=" * 50)
    print("修复内容：")
    print("✅ 解决了'main thread is not in main loop'错误")
    print("✅ 正确的tkinter线程处理")
    print("✅ 进度条实时更新")
    print("✅ 资源正确清理")
    print("✅ 遵循Python/tkinter最佳实践")
    print("=" * 50)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
