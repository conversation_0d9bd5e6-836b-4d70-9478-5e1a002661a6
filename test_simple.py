#!/usr/bin/env python3
"""
简单测试 - 验证核心功能

测试：
1. tkinter冲突
2. 取消功能
3. 线程安全
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from updater import check_update

def test_basic():
    """基本功能测试"""
    print("🔧 简单功能测试")
    print("=" * 40)
    print("测试内容：")
    print("1. 版本显示是否正确")
    print("2. 取消按钮是否有效")
    print("3. 窗口关闭是否正常")
    print("4. 线程是否正确终止")
    print("=" * 40)
    
    try:
        # 测试实际场景：1.0 -> 1.8
        result = check_update("imei_tool", "1.0")
        print(f"测试结果: {result}")
        print("请观察：")
        print("- 对话框显示：当前版本1.0，新版本1.8")
        print("- 进度窗口标题：正在下载 1.8")
        print("- 取消按钮是否能立即停止下载")
        print("- 窗口关闭后程序是否正常结束")
        
    except Exception as e:
        print(f"测试异常: {e}")

if __name__ == "__main__":
    test_basic()
