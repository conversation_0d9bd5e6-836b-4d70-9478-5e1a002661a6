# 专业级跨平台更新模块 - 简洁优雅版本

## 概述

这是一个经过彻底重构和修复的Python更新模块，解决了原版本中的所有问题：

### 🔧 修复的问题
- ✅ **进度条不更新问题** - 彻底修复GUI进度条实时更新
- ✅ **tkinter冲突问题** - 避免与其他项目的tkinter实例冲突
- ✅ **代码复杂度过高** - 重构为简洁优雅的架构
- ✅ **Python最佳实践** - 添加类型提示、改进异常处理、优化日志配置
- ✅ **跨平台兼容性** - 完善的Windows/Linux/Mac支持

### 🌟 特性
- 🚀 **简洁的API** - 只需几行代码即可集成
- 🎯 **GUI进度条** - 实时显示下载进度，用户体验优秀
- 🔄 **自动ZIP解压** - 支持ZIP包自动解压安装
- 🛡️ **线程安全** - 避免多实例冲突
- 📱 **跨平台** - Windows/Linux/Mac全平台支持
- 🎨 **避免冲突** - 独立的tkinter实例，不影响其他GUI

## 快速开始

### 基本用法

```python
from update import UpdateManager

# 手动检查更新
updater = UpdateManager("your_software", "1.0.0")
updater.check_once()
```

### 后台自动检查

```python
from update import UpdateManager

# 启动后台自动检查（每小时检查一次）
updater = UpdateManager("your_software", "1.0.0", check_interval=3600)
updater.start_background_check()

# 程序结束时停止
updater.stop()
```

### 便利函数

```python
from update import check_update_once, start_update_checker

# 简单的一次性检查
has_update = check_update_once("your_software", "1.0.0")

# 启动后台检查器
updater = start_update_checker("your_software", "1.0.0")
```

## API 文档

### UpdateManager 类

#### 构造函数
```python
UpdateManager(
    software_name: str,          # 软件名称
    current_version: str,        # 当前版本号
    base_url: str = "https://unlockoko.com",  # 服务器地址
    check_interval: int = 3600   # 检查间隔(秒)
)
```

#### 主要方法
- `check_once() -> bool` - 手动检查一次更新
- `start_background_check()` - 启动后台检查
- `stop()` - 停止所有活动

### 便利函数

#### check_update_once
```python
def check_update_once(
    software_name: str, 
    current_version: str, 
    base_url: str = "https://unlockoko.com"
) -> bool
```

#### start_update_checker
```python
def start_update_checker(
    software_name: str, 
    current_version: str, 
    base_url: str = "https://unlockoko.com"
) -> UpdateManager
```

## 集成到项目中

### 1. 避免tkinter冲突

模块使用独立的tkinter实例，不会与您的主程序GUI冲突：

```python
import tkinter as tk
from update import UpdateManager

# 您的主程序GUI
root = tk.Tk()
# ... 您的GUI代码 ...

# 更新检查（不会冲突）
updater = UpdateManager("your_app", "1.0.0")
updater.check_once()

root.mainloop()
```

### 2. 在程序启动时检查

```python
def main():
    # 程序启动时检查更新
    from update import check_update_once
    check_update_once("your_app", "1.0.0")
    
    # 您的主程序逻辑
    # ...

if __name__ == "__main__":
    main()
```

### 3. 后台持续检查

```python
import atexit
from update import UpdateManager

# 启动后台检查
updater = UpdateManager("your_app", "1.0.0")
updater.start_background_check()

# 确保程序退出时停止
atexit.register(updater.stop)

# 您的主程序
# ...
```

## 服务器端配置

确保您的服务器提供以下API：

### 检查更新 API
```
GET /api/check_update?software_name=your_app&current_version=1.0.0

响应：
{
    "code": "100",
    "data": {
        "need_update": true,
        "version": "1.1.0",
        "download_url": "/api/download/your_app",
        "release_notes": "修复了一些问题"
    }
}
```

### 下载文件 API
```
GET /api/download/your_app

返回文件流，支持：
- .exe 文件（Windows可执行文件）
- .zip 文件（自动解压安装）
- 其他文件（手动安装）
```

## 测试

运行测试脚本验证功能：

```bash
python test_update.py
```

测试包括：
- 基本功能测试
- GUI进度条测试
- 后台检查测试

## 注意事项

1. **依赖项**：需要 `requests` 库
2. **权限**：更新可执行文件可能需要管理员权限
3. **防火墙**：确保网络连接正常
4. **日志**：模块使用标准logging，不保存到文件

## 更新日志

### v2.0.0 (当前版本)
- 🔧 彻底修复进度条不更新问题
- 🛡️ 解决tkinter冲突问题
- 🎨 重构为简洁优雅的架构
- 📝 添加完整的类型提示
- 🚀 优化性能和用户体验
- 📚 完善文档和示例

### v1.x.x (原版本)
- 基础更新功能
- 存在进度条和冲突问题

## 许可证

MIT License - 可自由使用和修改
