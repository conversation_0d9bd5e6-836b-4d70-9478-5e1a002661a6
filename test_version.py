#!/usr/bin/env python3
"""
测试版本比较逻辑

验证版本比较是否正确工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from updater import compare_versions

def test_version_comparison():
    """测试版本比较功能"""
    print("🔧 版本比较逻辑测试")
    print("=" * 50)

    test_cases = [
        # (当前版本, 远程版本, 期望结果, 说明)
        ("1.0.0", "1.0.1", True, "小版本更新"),
        ("1.0.0", "1.1.0", True, "中版本更新"),
        ("1.0.0", "2.0.0", True, "大版本更新"),
        ("1.0.0", "1.0.0", False, "版本相同"),
        ("1.0.1", "1.0.0", False, "远程版本更旧"),
        ("1.1.0", "1.0.9", <PERSON>alse, "远程版本更旧"),
        ("2.0.0", "1.9.9", False, "远程版本更旧"),
        ("1.0", "1.0.1", True, "版本格式不同1"),
        ("1", "1.0.1", True, "版本格式不同2"),
        ("1.0.0", "1.0", False, "版本格式不同3"),
        ("1.0.0-beta", "1.0.0", True, "带标签版本1 (beta < 正式版)"),
        ("1.0.0", "1.0.0-beta", False, "带标签版本2"),
        ("v1.0.0", "v1.0.1", True, "带v前缀"),
        ("1.0.0", "1.8", True, "实际测试场景"),
    ]

    passed = 0
    failed = 0

    for current, remote, expected, description in test_cases:
        try:
            result = compare_versions(current, remote)
            if result == expected:
                print(f"✓ {description}: {current} -> {remote} = {result}")
                passed += 1
            else:
                print(f"❌ {description}: {current} -> {remote} = {result} (期望: {expected})")
                failed += 1
        except Exception as e:
            print(f"❌ {description}: 异常 - {e}")
            failed += 1

    print("\n" + "=" * 50)
    print(f"测试结果: {passed} 通过, {failed} 失败")

    if failed == 0:
        print("🎉 所有版本比较测试通过！")
        return True
    else:
        print("❌ 版本比较测试失败，需要修复")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n🔧 边界情况测试")
    print("=" * 50)

    edge_cases = [
        ("", "1.0.0", True, "空版本号"),
        ("1.0.0", "", False, "远程空版本号"),
        ("abc", "1.0.0", True, "无效版本号1"),
        ("1.0.0", "xyz", False, "无效版本号2"),
        ("*******", "*******", True, "4位版本号"),
        ("*******", "*******", False, "4位版本号反向"),
    ]

    passed = 0
    failed = 0

    for current, remote, expected, description in edge_cases:
        try:
            result = compare_versions(current, remote)
            if result == expected:
                print(f"✓ {description}: '{current}' -> '{remote}' = {result}")
                passed += 1
            else:
                print(f"❌ {description}: '{current}' -> '{remote}' = {result} (期望: {expected})")
                failed += 1
        except Exception as e:
            print(f"❌ {description}: 异常 - {e}")
            failed += 1

    print(f"\n边界测试结果: {passed} 通过, {failed} 失败")
    return failed == 0

def main():
    """主测试函数"""
    print("版本比较逻辑全面测试")

    # 基本测试
    basic_ok = test_version_comparison()

    # 边界测试
    edge_ok = test_edge_cases()

    if basic_ok and edge_ok:
        print("\n🎉 所有测试通过！版本比较逻辑正确")
        return 0
    else:
        print("\n❌ 测试失败，需要修复版本比较逻辑")
        return 1

if __name__ == "__main__":
    sys.exit(main())
