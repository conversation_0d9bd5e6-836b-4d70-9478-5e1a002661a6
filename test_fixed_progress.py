#!/usr/bin/env python3
"""
测试修复后的进度条功能

专门验证update.py中的进度条是否能正常工作
"""

import sys
import os
import time
import threading

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from update import UpdateManager

def test_real_update_progress():
    """测试真实的更新进度条"""
    print("=" * 60)
    print("测试修复后的更新进度条功能")
    print("=" * 60)
    print("这将创建一个真实的更新窗口来测试进度条")
    print("请观察进度条是否随着百分比实时移动")
    print("=" * 60)
    
    try:
        # 创建更新管理器
        updater = UpdateManager("test_progress", "1.0.0")
        print("✓ 更新管理器创建成功")
        
        # 模拟有更新可用
        updater.update_info = {
            "version": "2.0.0",
            "download_url": "/test/download",
            "release_notes": "测试进度条修复"
        }
        
        print("✓ 模拟更新信息设置完成")
        print("正在启动下载窗口...")
        
        # 启动下载（这会创建GUI窗口）
        updater._start_download()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def test_progress_updater_directly():
    """直接测试ProgressUpdater类"""
    print("\n" + "=" * 60)
    print("直接测试ProgressUpdater类")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from tkinter import ttk
        from update import ProgressUpdater
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("ProgressUpdater直接测试")
        root.geometry("500x160")
        root.attributes('-topmost', True)
        
        # 居中显示
        root.update_idletasks()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - 500) // 2
        y = (screen_height - 160) // 2
        root.geometry(f"500x160+{x}+{y}")
        
        frame = ttk.Frame(root, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(frame, text="ProgressUpdater直接测试", 
                 font=('Arial', 10, 'bold')).pack(pady=(0, 10))
        
        # 进度条 - 完全按照update.py的配置
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            frame,
            variable=progress_var,
            length=450,
            mode='determinate',
            maximum=100.0
        )
        progress_bar.pack(pady=(0, 10))
        
        # 状态标签
        status_label = ttk.Label(frame, text="准备测试...")
        status_label.pack(pady=(0, 10))
        
        # 创建ProgressUpdater实例
        progress_updater = ProgressUpdater(root, progress_var, status_label)
        
        # 取消按钮
        ttk.Button(frame, text="关闭", command=root.destroy).pack()
        
        def run_progress_test():
            """运行进度测试"""
            print("开始ProgressUpdater测试...")
            
            # 测试1: 基本进度更新
            print("测试1: 基本进度更新 (0-100%)")
            for i in range(101):
                percentage = float(i)
                status_text = f"基本测试: {i}%"
                progress_updater.update_progress(percentage, status_text)
                time.sleep(0.05)  # 50ms间隔
                
            time.sleep(1)
            
            # 测试2: 快速更新
            print("测试2: 快速更新测试")
            for i in range(0, 101, 2):  # 每次跳2%
                percentage = float(i)
                status_text = f"快速测试: {i}%"
                progress_updater.update_progress(percentage, status_text)
                time.sleep(0.02)  # 20ms间隔
                
            time.sleep(1)
            
            # 测试3: 模拟真实下载
            print("测试3: 模拟真实下载场景")
            total_size = 5 * 1024 * 1024  # 5MB
            downloaded = 0
            chunk_size = 32 * 1024  # 32KB
            
            while downloaded < total_size:
                downloaded += chunk_size
                if downloaded > total_size:
                    downloaded = total_size
                    
                percentage = (downloaded / total_size) * 100
                status_text = f"{downloaded/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB ({percentage:.1f}%)"
                
                progress_updater.update_progress(percentage, status_text)
                time.sleep(0.01)  # 10ms间隔
                
            progress_updater.update_progress(100.0, "测试完成！")
            print("✅ ProgressUpdater测试完成！")
        
        # 启动测试线程
        test_thread = threading.Thread(target=run_progress_test, daemon=True)
        test_thread.start()
        
        print("ProgressUpdater测试窗口已创建")
        print("观察要点：")
        print("1. 进度条是否平滑移动")
        print("2. 百分比数字是否与进度条同步")
        print("3. 状态文字是否实时更新")
        
        # 运行GUI
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ ProgressUpdater测试失败: {e}")
        return False

def main():
    """主函数"""
    print("修复后的进度条功能测试")
    print("本测试将验证进度条修复是否成功")
    
    choice = input("\n选择测试类型:\n1. 真实更新进度条测试\n2. ProgressUpdater直接测试\n请输入 (1 或 2): ").strip()
    
    if choice == "1":
        success = test_real_update_progress()
    elif choice == "2":
        success = test_progress_updater_directly()
    else:
        print("无效选择，运行ProgressUpdater直接测试...")
        success = test_progress_updater_directly()
    
    if success:
        print("\n🎉 测试完成！")
        print("如果进度条能够平滑移动，说明修复成功！")
    else:
        print("\n❌ 测试失败，需要进一步调试")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
